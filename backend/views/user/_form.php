<?php

use common\models\ChipEcuDict;
use common\models\ChipTarifs;
use common\models\User;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $model backend\models\UserForm */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $roles yii\rbac\Role[] */
/* @var $rolesDescriptions yii\rbac\Role[] */
/* @var $permissions yii\rbac\Permission[] */
/* @var $modelProfile \common\models\UserProfile */
$script = <<< JS
     setLayoutLight();


JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_READY);
$url = \yii\helpers\Url::to(['/projects/find-clients1c']);
//if ($modelProfile !== null) {
//    Html::errorSummary($modelProfile, ['encode' => false]);
//}
//var_dump($rolesDescriptions); die;

?>
<?php $form = ActiveForm::begin(['id' => 'msform','enableAjaxValidation' => true, 'enableClientValidation' => false]) ?>
<?= Html::errorSummary($model, ['encode' => false]) ?>

<div class="col-lg-12 col-xl-12">
    <ul class="nav nav-tabs  tabs m-b-25" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" data-toggle="tab" href="#home1" role="tab"><?php echo Yii::t('backend', 'User account data')?></a>
        </li>
<!--        --><?php //if (\Yii::$app->authManager->checkAccess($model->model->id, 'msdealer')) { ?>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#profile1" role="tab"><?php echo Yii::t('backend', 'User contact data')?></a>
        </li>
<!--        --><?php //} ?>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#messages1" role="tab"><?php echo Yii::t('backend', 'User price data')?></a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#tools" role="tab"><?php echo Yii::t('backend', 'User tools data')?></a>
        </li>
        <?php if (\Yii::$app->authManager->checkAccess($model->model->id, 'dealerpersonal') || \Yii::$app->authManager->checkAccess($model->model->id, 'dealerbusiness')) { ?>
            <li class="nav-item">
                <a class="nav-link" data-toggle="tab" href="#user1Cdata" role="tab"><?php echo Yii::t('backend', 'User 1C data')?></a>
            </li>
        <?php } ?>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#telegram" role="tab"><?php echo Yii::t('backend', 'Account telegram users data')?></a>
        </li>
    </ul>
    <!-- Tab panes -->
    <div class="tab-content tabs card-block m-b-25">
        <div class="tab-pane active" id="home1" role="tabpanel">
            <fieldset class="">
                <?php echo $form->field($model, 'username')->textInput(['placeholder' => 'Username'])->label(false) ?>
                <?php echo $form->field($model, 'email')->textInput(['placeholder' => 'Email'])->label(false) ?>
                <?php echo $form->field($model, 'phone')->widget(\yii\widgets\MaskedInput::className(), [
                    'mask' => '(999) 999-99-99',
                ])->label(false) ?>
                <?php echo $form->field($model, 'password')->textInput(['placeholder' => 'Password'])->passwordInput()->label(false) ?>
                <input type='checkbox' id='toggle' value='0' onchange="togglePass();">&nbsp; <span id='toggleText'>Show password</span>
                <?php echo $form->field($model, 'status')->dropDownList(User::statuses())->label(false) ?>
                <?php echo $form->field($model, 'tarif_id')->dropDownList(ArrayHelper::map(common\models\ChipTarifs::find()->notDeleted()->all(),'id', 'title'))->label(false) ?>
                <div class="form-group col-md-4">
                <?php echo $form->field($model, 'status')->dropDownList(User::statuses())->label(false) ?>
                <?php echo $form->field($model, 'tarif_id')->dropDownList(ArrayHelper::map(common\models\ChipTarifs::find()->all(),'id', 'title'))->label(false) ?>
                <div class="form-group col-md-4">
                    <?php echo $form->field($model, 'roles')->checkboxList($roles) ?>
                </div>
                <div class="form-group col-md-4">
                    <?php echo $form->field($model, 'type')->radioList([1 => 'Personal', 2 => 'Business']) ?>
                </div>
                <div class="form-group col-md-4">
                    <?php echo $form->field($model, 'blocked')->radioList([1 => 'Yes', 0 => 'No',]) ?>
                </div>
            </fieldset>
        </div>
<?php //if (\Yii::$app->authManager->checkAccess($model->model->id, 'msdealer')) { ?>
        <div class="tab-pane" id="profile1" role="tabpanel">
            <fieldset class="col-md-6">
                <?php echo $form->field($model, 'company')->textInput(['placeholder' => 'Company name'])->label(false) ?>
                <?php echo $form->field($model, 'country')->textInput(['placeholder' => 'Country'])->label(false) ?>
                <?php echo $form->field($model, 'city')->textInput(['placeholder' => 'City'])->label(false) ?>
                <?php echo $form->field($model, 'zip')->textInput(['placeholder' => 'Zip code'])->label(false) ?>
                <?php echo $form->field($model, 'address')->textInput(['placeholder' => 'Company address'])->label(false) ?>
                <?php echo $form->field($model, 'firstname')->textInput(['placeholder' => 'Contact person (name & surname)'])->label(false) ?>
                <?php echo $form->field($model, 'skype')->textInput(['placeholder' => 'Telegram login'])->label(false) ?>
            </fieldset>
        </div>
<!--        --><?php //} ?>
        <div class="tab-pane" id="messages1" role="tabpanel">
            <fieldset class="col-md-6">
            <?php foreach (\common\models\ChipAddition::find()->all() as $option) {?>
                <label><?=$option->title?></label>
                <input type="text" name="price[<?=$option->id?>]" class="form-control" value="<?=isset($model->model->UserPriceArray[$option->id]) ? $model->model->UserPriceArray[$option->id]->price : ''?>"/>
            <?php } ?>
            </fieldset>
        </div>
        <div class="tab-pane" id="tools" role="tabpanel">
            <fieldset class="col-md-6">
                <label class="f-20 sub-title">Master</label>
                <?php foreach (\common\models\ChipReadmethod::find()->notDeleted()->where(['master' => 1])->all() as $tool) {?>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="UserForm[tools][]" class="" value="<?=$tool->id?>" <?=in_array($tool->id, (is_array($model->model->UserToolsArray) ? $model->model->UserToolsArray : [])) ? 'checked' : ''?>/> <?=$tool->title?>
                        </label>
                    </div>
                <?php } ?>
            </fieldset>
            <fieldset class="col-md-6">
                <label class="f-20 sub-title">Slave</label>
                <?php foreach (\common\models\ChipReadmethod::find()->notDeleted()->where(['master' => 0])->all() as $tool) {?>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="UserForm[tools][]" class="" value="<?=$tool->id?>" <?=in_array($tool->id, (is_array($model->model->UserToolsArray) ? $model->model->UserToolsArray : [])) ? 'checked' : ''?>/> <?=$tool->title?>
                        </label>
                    </div>
                <?php } ?>
            </fieldset>
        </div>
        <?php if (\Yii::$app->user->can('dealerpersonal') || \Yii::$app->user->can('dealerbusiness')) { ?>
            <div class="tab-pane" id="user1Cdata" role="tabpanel">
                <?php echo $form->field($model, 'data_1c')->hiddenInput(['placeholder' => 'Contractor', 'id' => 'data_1c'])->label(false) ?>
                <?php
                echo $form->field($model, 'guid_1c')->widget(Select2::classname(), [
                        'id' => 'guid_1c',
                    'initValueText' => 'Введите название, имя или номер телефона контрагента', // set the initial display text
                    'options' => ['placeholder' => 'Введите название, имя или номер телефона контрагента'],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 4,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => $url,
                            'delay' => 250,
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) { return {name:params.term}; }'),
//                            'processResults' => new JsExpression('function (data) { return { results: data}; }'),
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(contractor) { return contractor.text; }'),
                        'templateSelection' => new JsExpression('function (contractor) { return contractor.text; }'),
                    ],
                    'pluginEvents' => [
                        "change" => "function() { console.log(this.value);console.log(this.selectedOptions[0].innerText); $('#data_1c').val(this.selectedOptions[0].innerText); }",
                    ],
                ]);
                ?>

            </div>
        <?php } ?>
        <div class="tab-pane" id="telegram" role="tabpanel">
            <fieldset class="col-md-12">
                <?php foreach ($model->model->telegramAccounts as $option) {
                    $accountData = !empty($option->tg_account) ? json_decode($option->tg_account, true) : [];
                    $phone = $accountData['phone_number'] ?? '';
                    $first_name = $accountData['first_name'] ?? '';
                    $last_name = $accountData['last_name'] ?? '';
                    ?>
                    <div class="form-group">
                        <label><?=$phone .'  '. $first_name .'  '. $last_name?></label>
                        <button onclick="$(this).parent('.form-group').remove()"><?=Yii::t('backend', 'Delete')?></button>
                    </div>
                <?php } ?>
            </fieldset>
        </div>
    </div>
</div>

<div class="form-group col-md-6">
    <?php echo Html::submitButton(Yii::t('frontend', 'Save'), ['class' => 'btn btn-primary', 'name' => 'save-button']) ?>
</div>
<?php if ($model->model->status < 2) {?>
<div class="form-group col-md-6">
    <?php echo Html::a(Yii::t('frontend', 'Activate user'), Url::to(['user/activate/', 'id' =>$model->model->id ]), ['class' => 'btn btn-warning']) ?>
</div>
<?php } ?>

<?php ActiveForm::end() ?>

