<?php

/**
 * Простой тест для проверки работы processEncodingResult
 */

require_once __DIR__ . '/vendor/autoload.php';

use common\chip\externalIntegrations\alientech\Infrastructure\Facade\EncodingFacade;
use common\config\ContainerFactory;

try {
    echo "=== Тест processEncodingResult ===\n";
    
    // Создаем контейнер
    $container = ContainerFactory::createContainer();
    
    // Получаем фасад
    $encodingFacade = $container->get(EncodingFacade::class);
    
    echo "✓ EncodingFacade успешно создан\n";
    
    // Тестовые данные callback
    $callbackData = [
        'guid' => 'test-operation-123',
        'isCompleted' => true,
        'isSuccessful' => true,
        'result' => [
            'encodedFileUrls' => [
                'OBDEncoded' => 'https://api.alientech.to/download/test-file'
            ]
        ]
    ];
    
    echo "✓ Тестовые данные подготовлены\n";
    
    // Попробуем вызвать метод (ожидаем исключение, так как операция не существует)
    try {
        $result = $encodingFacade->processEncodingResult($callbackData);
        echo "✗ Неожиданно: метод не выбросил исключение\n";
    } catch (\RuntimeException $e) {
        if (strpos($e->getMessage(), 'Encoding operation not found') !== false) {
            echo "✓ Метод корректно обрабатывает отсутствующую операцию\n";
        } else {
            echo "✗ Неожиданное исключение: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== Тест завершен успешно ===\n";
    
} catch (\Exception $e) {
    echo "✗ Ошибка: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
