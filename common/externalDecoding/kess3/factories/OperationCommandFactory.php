<?php

namespace common\externalDecoding\kess3\factories;

use common\externalDecoding\kess3\commands\DecodeOperationCommand;
use common\externalDecoding\kess3\dto\OperationContext;
use common\externalDecoding\kess3\enums\OperationType;
use common\externalDecoding\kess3\interfaces\OperationCommandInterface;
use common\externalDecoding\kess3\interfaces\OperationValidatorInterface;
use common\externalDecoding\kess3\interfaces\OperationStrategyInterface;
use common\models\ProjectFiles;

/**
 * Фабрика для создания команд операций
 *
 * Создает соответствующие команды для разных типов операций
 */
class OperationCommandFactory
{
    public function __construct(
        private OperationValidatorInterface $validator,
        private OperationStrategyFactory $strategyFactory
    ) {}

    /**
     * Создает команду по типу операции и контексту
     */
    public function create(OperationContext $context): OperationCommandInterface
    {
        $operationType = OperationType::fromInt($context->getOperationType());

        if (!$operationType) {
            throw new \InvalidArgumentException(
                'Unsupported operation type: ' . $context->getOperationType()
            );
        }

        $strategy = $this->strategyFactory->create($operationType);

        return match($operationType) {
            OperationType::KESS3_DECODING => new DecodeOperationCommand(
                $context,
                $this->validator,
                $strategy
            ),
            OperationType::KESS3_ENCODING_OBD => throw new \Exception('OBD encoding not implemented yet'),
            OperationType::KESS3_ENCODING_BOOT => throw new \Exception('BOOT encoding not implemented yet'),
        };
    }

    /**
     * Создает команду декодинга
     */
    public function createDecodeCommand(
        ProjectFiles $file,
        string $callbackUrl,
        array $userInfo,
        array $options = []
    ): DecodeOperationCommand {
        $context = OperationContext::forDecoding(
            file: $file,
            callbackUrl: $callbackUrl,
            userInfo: $userInfo,
            options: $options
        );

        $strategy = $this->strategyFactory->create(OperationType::KESS3_DECODING);

        return new DecodeOperationCommand($context, $this->validator, $strategy);
    }

    /**
     * Создает команду кодинга OBD
     */
    public function createOBDEncodeCommand(
        int $projectId,
        string $callbackUrl,
        array $userInfo,
        array $options = []
    ): OperationCommandInterface {
        throw new \Exception('OBD encoding not implemented yet');
    }

    /**
     * Создает команду кодинга BOOT
     */
    public function createBootEncodeCommand(
        int $projectId,
        string $callbackUrl,
        array $userInfo,
        array $options = []
    ): OperationCommandInterface {
        throw new \Exception('BOOT encoding not implemented yet');
    }

    /**
     * Создает команду из массива параметров
     */
    public function createFromArray(array $params): OperationCommandInterface
    {
        $requiredFields = ['operationType'];
        foreach ($requiredFields as $field) {
            if (!isset($params[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        $operationType = OperationType::fromInt($params['operationType']);
        if (!$operationType) {
            throw new \InvalidArgumentException(
                'Invalid operation type: ' . $params['operationType']
            );
        }

        // Создаем контекст из параметров
        $context = new OperationContext(
            operationType: $params['operationType'],
            projectId: $params['projectId'] ?? null,
            fileId: $params['fileId'] ?? null,
            userId: $params['userId'] ?? null,
            userInfo: $params['userInfo'] ?? [],
            options: $params['options'] ?? [],
            callbackUrl: $params['callbackUrl'] ?? null,
            metadata: $params['metadata'] ?? []
        );

        // Если указан fileId, загружаем файл
        if (isset($params['fileId'])) {
            $file = ProjectFiles::findOne($params['fileId']);
            if ($file) {
                $context->setFile($file);
            }
        }

        return $this->create($context);
    }



    /**
     * Проверяет, поддерживается ли тип операции
     */
    public function supports(int $operationType): bool
    {
        return OperationType::fromInt($operationType) !== null;
    }

    /**
     * Возвращает список поддерживаемых типов операций
     */
    public function getSupportedOperationTypes(): array
    {
        return array_map(fn($type) => $type->value, OperationType::all());
    }
}
