<?php

namespace common\externalDecoding\kess3\services;

use common\externalDecoding\kess3\dto\ErrorHandlingResult;
use common\externalDecoding\kess3\enums\ErrorSeverity;
use common\externalDecoding\kess3\exceptions\OperationException;
use common\externalDecoding\kess3\interfaces\ErrorHandlerInterface;

/**
 * Сервис для обработки ошибок операций KESS3
 * 
 * Централизованная обработка ошибок с поддержкой retry логики
 */
class OperationErrorHandler implements ErrorHandlerInterface
{
    public function __construct(
        private array $config = []
    ) {
        // Конфигурация по умолчанию
        $this->config = array_merge([
            'maxRetries' => [
                ErrorSeverity::CRITICAL->value => 0,
                ErrorSeverity::HIGH->value => 2,
                ErrorSeverity::MEDIUM->value => 3,
                ErrorSeverity::LOW->value => 5,
                ErrorSeverity::INFO->value => 0,
            ],
            'retryDelays' => [
                ErrorSeverity::HIGH->value => 30,
                ErrorSeverity::MEDIUM->value => 60,
                ErrorSeverity::LOW->value => 120,
            ],
            'notificationThreshold' => [
                ErrorSeverity::CRITICAL->value,
                ErrorSeverity::HIGH->value,
            ],
        ], $this->config);
    }

    /**
     * Обрабатывает ошибку операции
     */
    public function handleError(OperationException $exception): ErrorHandlingResult
    {
        $severity = $exception->getSeverity();

        // Логируем ошибку
        $this->logError($exception);

        // Определяем действие
        if ($this->shouldRetry($exception)) {
            $maxRetries = $this->getMaxRetries($exception);
            $retryDelay = $this->getRetryDelay(1, $exception);

            return ErrorHandlingResult::retry(
                retryDelay: $retryDelay,
                maxRetries: $maxRetries,
                reason: "Retrying due to {$severity->value} error",
                metadata: [
                    'severity' => $severity->value,
                    'operationGuid' => $exception->getOperationGuid(),
                    'operationType' => $exception->getOperationType()?->value,
                ]
            );
        }

        // Если критическая ошибка или исчерпаны попытки
        if ($severity === ErrorSeverity::CRITICAL) {
            $this->notifyError($exception);
            
            return ErrorHandlingResult::escalate(
                reason: "Critical error requires immediate attention",
                metadata: [
                    'severity' => $severity->value,
                    'requiresNotification' => true,
                ]
            );
        }

        // Обычная неудача
        return ErrorHandlingResult::fail(
            reason: "Operation failed with {$severity->value} error",
            metadata: [
                'severity' => $severity->value,
                'operationGuid' => $exception->getOperationGuid(),
            ]
        );
    }

    /**
     * Определяет, нужно ли повторить операцию
     */
    public function shouldRetry(OperationException $exception): bool
    {
        $severity = $exception->getSeverity();

        // Критические ошибки не повторяем
        if ($severity === ErrorSeverity::CRITICAL) {
            return false;
        }

        // Проверяем, поддерживает ли операция повторы
        if (!$exception->isRetryable()) {
            return false;
        }

        // Проверяем специфичные ошибки
        $message = strtolower($exception->getMessage());
        
        // Не повторяем ошибки валидации
        if (str_contains($message, 'validation failed') ||
            str_contains($message, 'invalid format') ||
            str_contains($message, 'file not found')) {
            return false;
        }

        // Не повторяем ошибки аутентификации
        if (str_contains($message, 'authentication failed') ||
            str_contains($message, 'access denied') ||
            str_contains($message, 'unauthorized')) {
            return false;
        }

        // Повторяем временные ошибки
        if (str_contains($message, 'timeout') ||
            str_contains($message, 'connection') ||
            str_contains($message, 'service unavailable') ||
            str_contains($message, 'temporary')) {
            return true;
        }

        // По умолчанию повторяем для средних и низких ошибок
        return in_array($severity, [ErrorSeverity::MEDIUM, ErrorSeverity::LOW]);
    }

    /**
     * Возвращает задержку перед повтором
     */
    public function getRetryDelay(int $attemptNumber, OperationException $exception): int
    {
        $severity = $exception->getSeverity();
        $baseDelay = $this->config['retryDelays'][$severity->value] ?? 60;

        // Экспоненциальная задержка с jitter
        $exponentialDelay = $baseDelay * (2 ** ($attemptNumber - 1));
        $jitter = mt_rand(0, min(30, $exponentialDelay * 0.1));

        return min($exponentialDelay + $jitter, 300); // Максимум 5 минут
    }

    /**
     * Возвращает максимальное количество попыток
     */
    public function getMaxRetries(OperationException $exception): int
    {
        $severity = $exception->getSeverity();
        return $this->config['maxRetries'][$severity->value] ?? 3;
    }

    /**
     * Классифицирует ошибку по серьезности
     */
    public function classifyError(OperationException $exception): string
    {
        return $exception->getSeverity()->value;
    }

    /**
     * Логирует ошибку
     */
    public function logError(OperationException $exception, array $context = []): void
    {
        $severity = $exception->getSeverity();
        $logLevel = $this->getLogLevel($severity);

        $logContext = array_merge([
            'operationGuid' => $exception->getOperationGuid(),
            'operationType' => $exception->getOperationType()?->value,
            'severity' => $severity->value,
            'context' => $exception->getContext(),
            'trace' => $exception->getTraceAsString(),
        ], $context);

        // Используем стандартное логирование PHP
        $message = sprintf(
            "[KESS3 Operation Error] %s (GUID: %s, Type: %s, Severity: %s)",
            $exception->getMessage(),
            $exception->getOperationGuid() ?? 'unknown',
            $exception->getOperationType()?->value ?? 'unknown',
            $severity->value
        );

        error_log($message . ' Context: ' . json_encode($logContext));

        // TODO: Интегрировать с системой логирования проекта
        // $this->logger->log($logLevel, $message, $logContext);
    }

    /**
     * Отправляет уведомление об ошибке
     */
    public function notifyError(OperationException $exception, array $context = []): void
    {
        $severity = $exception->getSeverity();

        // Проверяем, нужно ли отправлять уведомление
        if (!in_array($severity->value, $this->config['notificationThreshold'])) {
            return;
        }

        try {
            $notificationData = [
                'type' => 'operation_error',
                'severity' => $severity->value,
                'message' => $exception->getMessage(),
                'operationGuid' => $exception->getOperationGuid(),
                'operationType' => $exception->getOperationType()?->value,
                'timestamp' => date('c'),
                'context' => array_merge($exception->getContext(), $context),
            ];

            // TODO: Интегрировать с системой уведомлений
            // $this->notificationService->sendErrorNotification($notificationData);

            // Пока просто логируем
            error_log("[KESS3 Error Notification] " . json_encode($notificationData));

        } catch (\Exception $e) {
            error_log("Failed to send error notification: " . $e->getMessage());
        }
    }

    /**
     * Возвращает уровень логирования для серьезности
     */
    private function getLogLevel(ErrorSeverity $severity): string
    {
        return match($severity) {
            ErrorSeverity::CRITICAL => 'critical',
            ErrorSeverity::HIGH => 'error',
            ErrorSeverity::MEDIUM => 'warning',
            ErrorSeverity::LOW => 'notice',
            ErrorSeverity::INFO => 'info',
        };
    }

    /**
     * Создает обработчик ошибок с кастомной конфигурацией
     */
    public static function create(array $config = []): self
    {
        return new self($config);
    }

    /**
     * Создает обработчик для продакшена
     */
    public static function forProduction(): self
    {
        return new self([
            'maxRetries' => [
                ErrorSeverity::CRITICAL->value => 0,
                ErrorSeverity::HIGH->value => 1,
                ErrorSeverity::MEDIUM->value => 2,
                ErrorSeverity::LOW->value => 3,
                ErrorSeverity::INFO->value => 0,
            ],
            'retryDelays' => [
                ErrorSeverity::HIGH->value => 60,
                ErrorSeverity::MEDIUM->value => 120,
                ErrorSeverity::LOW->value => 300,
            ],
        ]);
    }

    /**
     * Создает обработчик для разработки
     */
    public static function forDevelopment(): self
    {
        return new self([
            'maxRetries' => [
                ErrorSeverity::CRITICAL->value => 0,
                ErrorSeverity::HIGH->value => 3,
                ErrorSeverity::MEDIUM->value => 5,
                ErrorSeverity::LOW->value => 10,
                ErrorSeverity::INFO->value => 0,
            ],
            'retryDelays' => [
                ErrorSeverity::HIGH->value => 5,
                ErrorSeverity::MEDIUM->value => 10,
                ErrorSeverity::LOW->value => 15,
            ],
        ]);
    }

    /**
     * Получает статистику обработки ошибок
     */
    public function getErrorStats(): array
    {
        // TODO: Реализовать сбор статистики
        return [
            'totalErrors' => 0,
            'retriedErrors' => 0,
            'escalatedErrors' => 0,
            'notificationsSent' => 0,
        ];
    }

    /**
     * Проверяет здоровье обработчика ошибок
     */
    public function healthCheck(): array
    {
        return [
            'status' => 'healthy',
            'timestamp' => date('c'),
            'config' => [
                'maxRetries' => $this->config['maxRetries'],
                'retryDelays' => $this->config['retryDelays'],
                'notificationThreshold' => $this->config['notificationThreshold'],
            ],
            'stats' => $this->getErrorStats(),
        ];
    }
}
