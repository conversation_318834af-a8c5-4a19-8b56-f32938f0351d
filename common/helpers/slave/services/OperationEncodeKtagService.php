<?php


namespace common\helpers\slave\services;



use common\helpers\MessageHelper;
use common\helpers\ProjectHelper;
use common\models\ProjectFiles;
use common\models\SlaveFile;
use common\models\SlaveOperation;
use Yii;

class OperationEncodeKtagService extends OperationKtagService
{
    protected $oldSlaveOperation;

    public function __construct($fileObject)
    {
        $this->fileObject = $fileObject;
        $this->projectFile = $this->fileObject->getProjectFile();
        $this->slaveFile = $this->fileObject->getSlaveFile();
        $this->oldSlaveOperation = SlaveOperation::find()->where(['slotGUID' => $this->slaveFile->slot_guid])->one();
    }

    protected function processOperation(): ?bool
    {
        $this->log('processOperation////');

        if (is_null(parent:: processOperation()))
        {
            return null;
        }

        $this->slotGuid = $this->slaveOperation->result->ktagFileSlotGUID;

        $fileData = $this->downloadFile($this->slaveOperation->result->encodedFileURL);

        return $this->saveFileToProject($fileData);
    }

    protected function startMainMethod()
    {
        $this->log('startMainMethod////');
//        $fileBySlot = $this->getFileBySlot(self::FileTypeModified);

        $files = json_decode($this->slaveFile->file_guid, true);

        $this->log('////startMainMethod');

        if (isset($files) && (!empty($files))) {
            return $this->encodeKtagFiles($files);
        }

        return null;
    }

    private function saveFileToProject($fileData): ?bool
    {
        $this->log('saveFileToProject////');

        $fileSuffix = '_CODED';

        $fileName = $this->slaveOperation->result->name.$fileSuffix;

        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

        if (!$this->saveFileToStorage($filePath, $fileData->data))
        {
            $this->addError('saveFileToStorage_false');
            $this->log($this->getErrors());
            return null;
        }

        $project = $this->projectFile->project;

        $fileSuffix = ProjectHelper::PROJECT_FILE_SUFFIX_MODIFIED.'_v' . ($project->modFilesCount + 1);
        $fileAttrs = [];
        $fileAttrs['orig'] = ProjectHelper::PROJECT_FILE_MODIFIED;

        if(($modDecFile = ProjectFiles::find()
                ->where(['project_id' => $project->id, 'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED])
                ->notDeleted()
                ->orderBy(['id' => SORT_DESC])
                ->one()) !== null) {
            if ($modDecFile->orig == ProjectHelper::PROJECT_FILE_ORIGINAL) {
                $fileSuffix = ProjectHelper::PROJECT_FILE_SUFFIX_ORIGINAL;
            }
            $fileAttrs['orig'] = $modDecFile->orig;
            $fileAttrs['can_download'] = $modDecFile->can_download;
            $fileAttrs['comment'] = $modDecFile->comment;
        }

        $messageStart = 'Modified file uploaded for #'.$project->id;

        $fileName = str_replace([' ', '/'], '_', $project->brand->title) . '_' . str_replace([' ', '/'], '_', $project->model->title) . '_' . str_replace([' ', '/'], '_', $project->year) . '_'. str_replace([' ', '/'], '_', $project->engine->title).$fileSuffix;

        $projectFile = new ProjectFiles();
        $projectFile->setAttributes($fileAttrs);
        $projectFile->setAttributes([
            'type' => 'external',
            'title' => $fileName,
            'project_id' => $project->id,
            'file_id' => $this->projectFile->id,
            'alientech_operation_id' => 999,
            'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_ENCODED,
            'path' => $filePath,
            'filename' => $fileName,
            'hash' => yii::$app->security->generateRandomString(12),
        ]);

        if (!$projectFile->save(false)) {
            $this->addError('save_projectFile_false');
            $this->addError($projectFile->errors);
            $this->log($this->getErrors());
            return null;
        }

        if (!$this->setMessagesForEncodedFile($project, $projectFile, $messageStart))
        {
            $this->addError('saveFileToStorage_false');
            $this->log($this->getErrors());
            return null;
        }

        $this->log('////saveFileToProject');

        return true;
    }

}
