<?php


namespace common\helpers\slave\services;


use common\helpers\slave\file\File;
use common\helpers\slave\operation\Operation;
use common\models\ProjectFiles;
use yii\httpclient\Client;

class OperationKessService extends OperationBaseService
{
    public $apiFileTypePart = 'kessv2';
    protected $data;
    protected $fileObject;
    protected $operation;
    protected $slaveOperation;

    public function __construct(File $file)
    {
        $this->fileObject = $file;
        $this->checkOperation();
    }

    public function checkOperation()
    {
        switch ($this->fileObject->getSlaveFile()->getStatus()) {
            case (File::SlaveFileStatusDecode) :
                $this->operation = new OperationDecodeKessService($this->fileObject);
                break;
            case (File::SlaveFileStatusUploadMod) :
                $this->operation = new UploadModKessFileService($this->fileObject);
                break;
            case (File::SlaveFileStatusEncodeMod) :
                $this->operation = new OperationEncodeKessService($this->fileObject);
                break;
            default : return null;
        }
    }

    public function getOperationData()
    {
        $this->log('getOperationData////');
        if ($this->interval > 0) {
            $this->data = $this->checkOperationData();
        } else {
            $this->data = $this->startMainMethod();
        }
        $this->log('////getOperationData');
    }

    protected function processOperation(): ?bool
    {
        $this->log('processOperation////');

//        $this->setMessageToProject("kess_operation_", "start", "start process");

        $this->getOperationData();

        if (empty($this->data)) {
            $this->addError('empty_data');
            $this->log($this->getErrors());
            return null;
        }

        if (!isset($this->data->guid)) {
            $this->addError('empty_data_guid');
            $this->log($this->getErrors());
//            $this->setMessageToProject("kess_operation_", "sleep", json_encode($this->data));
            return null;
        }

        $this->slaveOperation = $this->createOperation($this->data);
        $this->slaveOperation->setFileId($this->fileObject->getSlaveFile()->getFileId());
        $this->slaveOperation->save();

//        parent::checkOperationReadmethodErrors();

        if (isset($this->slaveOperation->slotGUID)) {
            $this->slaveFile->setAttribute('slot_guid', $this->slaveOperation->slotGUID);
            $this->slaveFile->save();
        }

        $this->log('$this->slaveOperation->isCompleted');
        $this->log($this->slaveOperation->isCompleted);

        if (!$this->slaveOperation->isCompleted) {
            $this->setInterval($this->slaveOperation->recommendedPollingInterval);
            $this->log('sleep '. $this->getInterval()*2);
//            $this->setMessageToProject("kess_operation_".$this->slaveOperation->asyncOperationType, "sleep", "sleep");
            sleep($this->getInterval()*2);
            return self::processOperation();
        }


        if (!$this->slaveOperation->isSuccessful || $this->slaveOperation->hasFailed) {
            $this->setMessageToProject("error", json_encode($this->slaveOperation->error), json_encode($this->slaveOperation->error));
            $this->addError(['isSuccessful_false' => $this->slaveOperation->error]);
            $this->log($this->slaveOperation);
            $this->log($this->getErrors());
            return null;
        }

//        if ($this->slaveOperation->hasFailed) {
//            $this->setMessageToProject("hasFailed", json_encode($this->slaveOperation->error), json_encode($this->slaveOperation->error));
//            $this->addError(['hasFailed_true' => $this->slaveOperation->error]);
//            $this->log($this->slaveOperation);
//            $this->log($this->getErrors());
//            return null;
//        }

//        $this->setMessageToProject("kess_operation_".$this->slaveOperation->asyncOperationType, "finish", "finish");

        return true;
    }

    public function uploadMod(ProjectFiles $projectFile, $slotGuid)
    {
        $url = '/api/'.$this->apiFileTypePart.'/upload-modified-file/'.$this->userPerformed.'/'.$slotGuid;

        return parent::uploadModFile($url, $slotGuid, $projectFile);
    }

    public function getOperation()
    {
        return $this->operation;
    }

}
