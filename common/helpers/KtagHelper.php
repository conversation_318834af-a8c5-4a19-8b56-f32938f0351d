<?php

namespace common\helpers;

use common\commands\AlientechProcessProjectsCommand;
use common\models\AlientechLog;
use common\models\AlientechOperation;
use common\models\ChipEcu;
use common\models\ProjectFileDecoded;
use common\models\ProjectFiles;
use common\models\ProjectMessages;
use common\models\Projects;
use common\models\SeoChipPage;
use common\models\User;
use Yii;
use yii\helpers\Inflector;
use yii\helpers\Url;
use yii\httpclient\Client;

class KtagHelper extends AlientechApiHelper
{


    const KtagFileComponentTypeMicro = 'Micro';
    const KtagFileComponentTypeFlash = 'Flash';
    const KtagFileComponentTypeEEPROM = 'EEPROM';
    const KtagFileComponentTypeMapFile = 'MapFile';

    const KtagFileTypeReadBackup = 1;
    const KtagFileTypeID = 2;
    const KtagFileTypeDecodedMicro = 3;
    const KtagFileTypeDecodedFlash = 4;
    const KtagFileTypeDecodedEEPROM = 5;
    const KtagFileTypeDecodedMap = 6;
    const KtagFileTypeModifiedMicro = 7;
    const KtagFileTypeModifiedFlash = 8;
    const KtagFileTypeModifiedEEPROM = 9;
    const KtagFileTypeModifiedMap = 10;
    const KtagFileTypeEncodedBackup = 11;

    const FileTypeEncoded = 'EncodedBackup';

    const HELPER_TYPE = 'KTAG';

//    The type of file. It can be
//one of the following
//values:
//1: Read backup
//2: ID
//3: Decoded Micro
//4: Decoded Flash
//5: Decoded EEPROM
//6: Decoded Map File
//7: Modified Micro
//8: Modified Flash
//9: Modified EEPROM
//10: Modified Map File
//11: Encoded backup

    public static function fileTypes() {
        return [
            self::KtagFileComponentTypeMicro,
            self::KtagFileComponentTypeFlash,
            self::KtagFileComponentTypeEEPROM,
            self::KtagFileComponentTypeMapFile,
        ];
    }

    //--------------------------------------------------------ref-3--------------------------------------------------------------//

    /**
     * REV-3 K-TAG OPERATIONS Decode a backup
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function decodeFile(ProjectFiles $projectFile = null, $request, $userPerformed)
    {

        $result = null;

        $url = '/api/ktag/decode-read-file/'.$userPerformed;
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('POST')
            ->setUrl($url)
            ->addFile('readFile', $projectFile->path)
            ->send();

        return $response;
    }

    /**
     * REV-3 Проверка количества доступных слотов
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public function hasOpenFileSlots($slots):bool
    {
        $result = true;

        $openedSlots = 0;

        if (count($slots) > 0) {
            foreach ($slots as $slot) {
                if (!$slot->isClosed) {
                    $openedSlots++;
                }
            }
            if ($openedSlots >= self::MAXIMUM_KTAG_FILE_SLOTS) {
                $result = false;
            }
        }

        return $result;

    }

    /**
     * REV-3 K-TAG FILE SLOTS AND FILES
     * Get the list of K-TAG File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function getFileSlots($request)
    {
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('GET')
            ->setUrl('/api/ktag/file-slots')
            ->send();
        return $response;
    }

    /**
     * REV-3 K-TAG Получение слота
     * Get a K-TAG File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function getFileSlot($fileSlotGUID = '', $request)
    {
        $url = '/api/ktag/file-slots/'.$fileSlotGUID;
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('GET')
            ->setUrl($url)
            ->send();
        return $response;
    }

    /**
     * REV-3 Re-open a closed K-TAG File Slot
     * @param string $fileSlotGUID
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function tryReopenFileSlot($fileSlotGUID = '', $request)
    {
        $url = '/api/ktag/file-slots/'.$fileSlotGUID.'/reopen';
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('POST')
            ->setUrl($url)
            ->send();
        return $response;
    }
    /**
     * REV-3 K-TAG скачивание файла
     * Get a specific KESSv2 File
     * @param AlientechOperation|null $operation
     * @param null $fileData
     * @return array
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */

    public static function downloadKtagFile(
        AlientechOperation $operation = null,
        $fileData = null,
        $fileSuffix = null,
        $fileUrl = null,
        $step = parent::OperationStep_file_downloaded
    )
    {
        parent::logToFile(' --- downloadKtagFile--$operation-'.$operation->id);

        $result = [];

        $result['success'] = false;

        if (is_null($operation) || is_null($fileData)) {
            return $result;
        }

        if (parent::authorise()) {
            $fileSuffix = $fileSuffix ?? '_DECODED';

            $components = $fileData->components ?? null;
            if (!is_null($components)) {
                foreach ($components as $key => $filePart) {
                    $fileName = $fileData->name.$fileSuffix.'_'.$filePart->type;
                    $result['filesData'][$key]['fileName'] = $fileName;
                    $result['filesData'][$key]['component'] = $filePart->type;
                    $client = parent::getClient();
                    $response = $client->createRequest()
                        ->setMethod('GET')
                        ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
                        ->setUrl($filePart->decodedFileURL)
                        ->send();

                    $resp = $response->getContent();

                    parent::checkErrors($response);

                    parent::logAlientechApi($operation->guid, $fileUrl, $resp);

                    $respData = json_decode($resp);

                    if($respData->data) {

                        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

                        $result['filesData'][$key]['filePath'] = $filePath;

                        $data_decoded = base64_decode ($respData->data);

                        $savedFile = fopen ($filePath,'w');

                        if (fwrite ($savedFile, $data_decoded)) {
                            fclose ($savedFile);
                            $result['filesData'][$key]['success'] = true;
                            $result['success'] = true;
                        }
                    }
                }
            }
        }

        return $result;

    }

    /**
     * REV-3 K-TAG скачивание файла
     * Get a specific KESSv2 File
     * @param AlientechOperation|null $operation
     * @param null $fileData
     * @return array
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function downloadFile(
        $request,
        $fileUrl = ''
    )
    {
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('GET')
            ->setUrl($fileUrl)
            ->send();
        return $response;
    }
    /**
     * REV-3 K-TAG FILE SLOTS AND FILES
     * Close an open K-TAG File Slot
     * Get the list of KESSv2 File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function closeSlot($ktagFileSlotGUID = '', $request)
    {
        $url = '/api/ktag/file-slots/'.$ktagFileSlotGUID.'/close';
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('POST')
            ->setUrl($url)
            ->send();
        return $response;
    }


    /**
     * REV-3 Загрузка модифицированного файла на сервер алиентеч. Запускается когда Менеджер загружает в проект мод файл
     * @param ProjectFiles $projectFile
     * @return bool|mixed
     * @throws \yii\base\InvalidConfigException
     */
    public static function uploadMod(ProjectFiles $projectFile, $slotGuid, $request, $userPerformed, $accessToken)
    {
        $result = false;

        if (is_null($projectFile) || empty($slotGuid)) {
            return $result;
        }

        $url = '/api/ktag/upload-modified-component-file/'.$userPerformed.'/'.$slotGuid.'/'.$projectFile->component_name;

        $client = new Client([
            'baseUrl' => AlientechProcessProjectsCommand::$apiUrl,
            'contentLoggingMaxSize' => 1000000,
            'responseConfig' => [
                'format' => Client::FORMAT_JSON
            ],
        ]);

        $response = $client->createRequest()
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $accessToken])
            ->setMethod('PUT')
            ->setUrl($url)
            ->addFile('file', $projectFile->path)
            ->send();

        return $response;
    }

    /**
     * REV-3 K-TAG кодирование файла
     * @param $fileData
     * @return mixed|null
     * @throws \yii\base\InvalidConfigException
     */
    public function startEncodeFile($fileResult, $fileParts, $client, $userPerformed, $accessToken){

        $data = ['userCustomerCode' => $userPerformed, 'ktagFileSlotGUID' => $fileResult->ktagFileSlotGUID];

        $url = '/api/ktag/encode-file';
        foreach ($fileParts as $key => $filePart) {
            if (isset($filePart->guid)) {
                $data[$key] = $filePart->guid;
            }
        }

        $response = $client->createRequest()
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $accessToken])
            ->setFormat(Client::FORMAT_JSON)
            ->setMethod('POST')
            ->setUrl($url)
            ->setData($data)
            ->send();

        return $response;

    }

    /**
     * REV-2 K-TAG получение компонента файла
     * Get a K-TAG File from a K-TAG File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function getFileBySlot($fileResult, $fileType = 'ReadBackup', $request)
    {
        $ktagFileSlotGUID = $fileResult->ktagFileSlotGUID;
        $options = ['fileType' => $fileType];
        $response = $request
            ->setFormat(Client::FORMAT_URLENCODED)
            ->setMethod('GET')
            ->setUrl('/api/ktag/file-slots/'.$ktagFileSlotGUID.'/files/?'.http_build_query($options))
            ->send();
        return $response;
    }

    /**
     * REV-3 K-TAG скачивание кодированного файла
     * @param AlientechOperation $operation
     * @return array
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     */
    public static function downloadEncodedFile($fileData, $file)
    {
        $result = [];

        $result['success'] = false;

        if (is_null($fileData)) {
            return $result;
        }

        $fileSuffix = '_CODED';

        if($file->data) {

            $fileName = $fileData->name.$fileSuffix;

            $result['fileName'] = $fileName;

            $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

            $result['filePath'] = $filePath;

            $data_decoded = base64_decode ($file->data);

            $savedFile = fopen ($filePath,'w');

            if (fwrite ($savedFile, $data_decoded)) {
                fclose ($savedFile);
                $result['success'] = true;
            }

        }

        return $result;
    }


    //--------------------------------------------------------ref-3--------------------------------------------------------------//


    //--------------------------------------------------------ref-2--------------------------------------------------------------//


    /**
     * REV-2 Re-open a closed K-TAG File Slot
     * @param string $fileSlotGUID
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function tryReOpenKtagFileSlot($fileSlotGUID = '', $operation = null):bool
//    {
//        parent::logToFile(' --- tryReOpenKtagFileSlot--$fileSlotGUID-'.$fileSlotGUID);
//
//        $result = false;
//
//        $slotInfo = self::getKtagFileSlot($fileSlotGUID, $operation);
//
//        if (!$slotInfo->isClosed) {
//            return true;
//        }
//
//        if (parent::authorise()) {
//            $client = parent::getClient();
//
//            if (empty($fileSlotGUID)) {
//                return $result;
//            }
//            $url = '/api/ktag/file-slots/'.$fileSlotGUID.'/reopen';
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//                ->setUrl($url)
//                ->send();
//
//            $resultCheckOperation = parent::checkAlientechResponse($client, $response, $operation, ['tryReOpenKtagFileSlot', $url], false);
//
//            if (empty($resultCheckOperation)) {
//                return true;
//            }
//
//        }
//
//        return $result;
//
//    }




    /**
     * REV-2 K-TAG скачивание кодированного файла
     * @param AlientechOperation $operation
     * @param $fileData
     * @param string $fileSuffix
     * @param $encodedFileURL
     * @param int $OperationStep_encoded_file_downloaded
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
//    private static function downloadEncodedKtagFile(AlientechOperation $operation, $fileData, string $fileSuffix, $encodedFileURL, int $OperationStep_encoded_file_downloaded)
//    {
//        $result = [];
//
//        $result['success'] = false;
//
//        if (is_null($operation) || is_null($fileData)) {
//            return $result;
//        }
//
//        if (parent::authorise()) {
//            $fileSuffix = $fileSuffix ?? '_DECODED';
//
//            $file = self::getKtagFileBySlot($fileData->ktagFileSlotGUID, 'EncodedBackup');
//
//            if($file->data) {
//
//                $fileName = $fileData->name.$fileSuffix;
//
//                $result['fileName'] = $fileName;
//
//                $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;
//
//                $result['filePath'] = $filePath;
//
//                $data_decoded = base64_decode ($file->data);
//
//                $savedFile = fopen ($filePath,'w');
//
//                if (fwrite ($savedFile, $data_decoded)) {
//                    fclose ($savedFile);
//                    $result['success'] = true;
//                }
//
//            }
//        }
//        parent::logToFile(' --- downloadEncodedKtagFile--$operation-'.$operation->id);
//
//        return $result;
//
//    }





    //------------------------------------------------------/--ref-2--------------------------------------------------------------//






    //----------------------------------------------------------------------------------------------------------------------//




    /**
     * K-TAG FILE SLOTS AND FILES
     * Get a specific K-TAG File
     * @param $file
     * @return bool|mixed|\yii\httpclient\Response
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function getKtagFile($file)
//    {
//        $result = false;
//
//        if (empty($file)) {
//            return $result;
//        }
//
//        if (parent::authorise()) {
//            $client = parent::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//                ->setUrl('/api/ktag/file-slots/'.$file->slotGUID.'/files/'.$file->guid)
//                ->send();
//
//            $resp = $response->getContent();
//
//            parent::logAlientechApi('file', '/api/ktag/file-slots/'.$file->slotGUID.'/'.$file->guid, $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Search for K-TAG Files
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function searchKtagFilesBySlot($ktagFileSlotGUID = '', $fileType = '')
//    {
//        $result = false;
//
//        if (parent::authorise()) {
//            $client = parent::getClient();
//
//            if (empty($ktagFileSlotGUID)) {
//                return $result;
//            }
//
//            $options = ['i_ktagFileSlotGUID' => $ktagFileSlotGUID];
//
//            if (!empty($fileType)) {
//                $options = ['i_fileType' => $fileType];
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//                ->setUrl('/api/ktag/files/?'.http_build_query($options))
//                ->send();
//
//            $resp = $response->getContent();
//
//            parent::logAlientechApi('file-slots', '/api/ktag/files/?'.http_build_query($options), $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }







    /**
     * @param $file
     * @return mixed|null
     * @throws \yii\base\InvalidConfigException
     */
//    public static function encodeKtagFile($file){
//
//        $result = null;
//
//        $client = parent::getClient();
//
//        $client->requestConfig = ['format' => Client::FORMAT_JSON];
//
//        $response = $client->createRequest()
//            ->setMethod('POST')
//            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//            ->setUrl('/api/kessv2/encode-file')
//            ->setData(['userCustomerCode' => parent::$userPerformed, 'ktagFileSlotGUID' => $file->slotGUID, 'modifiedFileGUID' => $file->guid])
//            ->send();
//
//        $resp = $response->getContent();
//
//        parent::logAlientechApi('encode_file', '/api/kessv2/encode-file', $resp);
//
//        parent::checkErrors($response);
//
//        if (!empty($resp)) {
//            $result =  json_decode($resp);
//        } else {
//            $result =  $response;
//        }
//        return $result;
//    }

//---------------------------------------------------------------------------------------------------


    /**
     * Обновляет данные об операции в бд
     * @param null $operation
     * @param null $respData
     * @return bool
     */
//    public static function alientechOperationDecoded(AlientechOperation $operation = null)
//    {
//        $result = false;
//
//        if (is_null($operation)) {
//            return $result;
//        }
//
//        $operation->setAttributes([
////            'processed' => 1,
//            'step' => parent::OperationStep_decoded,
//        ]);
//
//        $operation->save();
//        $result = true;
//        return $result;
//    }

    /**
     * Обновляет данные об операции в бд
     * @param null $operation
     * @param null $respData
     * @return bool
     */
//    public static function completeAlientechOperation(AlientechOperation $operation = null, $respData = null, $step = parent::OperationStep_decoded)
//    {
//        $result = false;
//
//        if (is_null($operation) || is_null($respData)) {
//            return $result;
//        }
//
//        $operation->setAttributes([
//            'result_data' => json_encode($respData),
//            'completed' => 1,
//            'step' => $step,
//        ]);
//        $operation->save();
//        $result = true;
//
//        return $result;
//    }

    /**
     * Метод проверяет операцию с запросом информации на сервере Alientech
     * @param string $guid
     * @return bool|mixed
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function checkAlientechOperation($guid = '')
//    {
//        $result = false;
//
//        if (empty($guid)) {
//            return $result;
//        }
//
//        if (parent::authorise()) {
//            $url = '/api/async-operations/'.$guid;
//            $client = parent::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//                ->setUrl($url)
//                ->send();
//
//            $resp = $response->getContent();
//
//            $respData = json_decode($resp);
//
//            parent::logAlientechApi($guid, $url, $resp);
//
//            $result = $respData;
//        }
//
//        return $result;
//    }

//    public static function checkErrors($response)
//    {
//        $resp = $response->getContent();
//
//        if ($resp == 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS') {
//            return [
//                'status' => 'error',
//                'message' => $resp
//            ];
//
//        }
//        if ($resp == 'KESSV2_FILE_SLOT_IS_CLOSED') {
//            return [
//                'status' => 'error',
//                'message' => $resp
//            ];
//
//        }
//        return [
//            'status' => 'success',
//            'message' => ''
//        ];
//
//    }


//    public static function testDownloadKessFiles(string $string)
//    {
//
//
////        $slot = parent::getKessFileSlot($string);
//
////        $file = parent::getKessFileBySlot($string, 'Read');
//
//        $files = parent::searchKessFilesBySlot($string);
//
////        var_dump($slot);
////        var_dump($file);
////0af83fc5-150e-4d09-b2c2-b7cce0b5a8a6
////0af83fc5-150e-4d09-b2c2-b7cce0b5a8a6
//        if (!empty($files) && is_array($files)) {
//            foreach ($files as $file) {
//                $fileData = parent::getKessFile($file);
//
////                $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileData->name.'__'.$fileData->fileType;
//////Decode pdf content
////                $data_decoded = base64_decode ($fileData->data);
//////Write data back to pdf file
////                $savedFile = fopen ($filePath,'w');
////                fwrite ($savedFile,$data_decoded);
//////close output file
////                fclose ($savedFile);
////                echo 'Done';
//                var_dump($fileData);
//            }
//        }
//        die;
//    }


    /**
     * метод логирования всех обращений к апи алиентеч
     * @param null $guid
     * @param null $url
     * @param null $data
     */
//    public static function logAlientechApi($guid = null, $url = null, $data = null):void
//    {
//
//        if (!is_string($data)) {
//            $data = json_encode($data);
//        }
//
//        $operationLog = new AlientechLog();
//        $operationLog->setAttributes([
//            'guid' => $guid,
//            'url' => $url,
//            'resp_data' => $data,
//        ]);
//
//        $operationLog->save();
//
//    }

    /**
     * метод получения всех оригинальных кодированных файлов в незакрытых проектах которые созданы с типом софта слейв
     * @return array
     */
//    public static function getOrigEncFiles():array
//    {
//        $files =
//            ProjectFiles::find()
//                ->joinWith(['project' => function ($q) {
//                    $q->where('projects.status !='.ProjectHelper::STATUS_CLOSED);
//                }])
//                ->joinWith(['originalDecodedFiles decoded' => function ($q) {
//                    $q->where('decoded.id is null');
//                }])
//                ->with('project')
//                ->where(['project_files.file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_ENCODED])
//                ->andWhere('project_files.alientech_operation_id is null')
//                ->limit(5)
////                ->createCommand()->getRawSql();
//                ->all();
//        return $files ?? [];
//    }



//    public static function reOpenKessFileSlot($kessv2FileSlotGUID = ''):bool
//    {
//        $result = false;
//
//        if (parent::authorise()) {
//            $client = parent::getClient();
//
//            if (empty($kessv2FileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => parent::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/reopen')
//                ->send();
//
//            $resp = $response->getContent();
//
//            parent::logAlientechApi('reopen-file-slots', '/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/reopen', $resp);
//
//            $result = true;
//        }
//
//        return $result;
//
//    }




}
