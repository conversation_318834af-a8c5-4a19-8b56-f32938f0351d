<?php

namespace common\mail;

/**
 * Интерфейс для работы с отправкой email
 */
interface MailerInterface
{
    /**
     * Приоритет высокий
     */
    const PRIORITY_HIGH = 1;
    
    /**
     * Приоритет нормальный
     */
    const PRIORITY_NORMAL = 3;
    
    /**
     * Приоритет низкий
     */
    const PRIORITY_LOW = 5;
    
    /**
     * Создать сообщение
     *
     * @return \common\mail\MessageInterface
     */
    public function createMessage(): \common\mail\MessageInterface;
    
    /**
     * Отправить сообщение
     *
     * @param \common\mail\MessageInterface $message Сообщение
     * @return bool Результат отправки
     */
    public function send(\common\mail\MessageInterface $message): bool;
}
