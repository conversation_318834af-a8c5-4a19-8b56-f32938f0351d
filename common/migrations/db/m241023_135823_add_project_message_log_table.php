<?php

use yii\db\Migration;

/**
 * Class m241023_135823_add_project_message_log_table
 */
class m241023_135823_add_project_message_log_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%project_message_log}}', [
            'id' => $this->primaryKey(),
            'message_id' => $this->integer(11)->notNull()->defaultValue(0),
            'message_send_id' => $this->integer(11)->notNull()->defaultValue(0),
            'send_from' => $this->integer(11)->notNull()->defaultValue(0),
            'send_to' => $this->integer(11)->notNull()->defaultValue(0),
            'send_method' => $this->string(255)->notNull()->defaultValue(''),
            'is_successful' => $this->tinyInteger(1)->notNull()->defaultValue(0),
            'is_completed' => $this->tinyInteger(1)->null(),
            'created_at' => $this->timestamp()->null()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->null()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
        ]);
    }

    /**
     *
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%project_message_log}}');
    }
}