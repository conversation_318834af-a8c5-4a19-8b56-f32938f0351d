<?php

use common\chip\alientech\services\AlientechService;
use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\alientech\Application\Decoding\EventHandler\DecodingEventHandler;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\ProcessDecodingResultHandler;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\EventHandler\EncodingCompletedEventHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\EventHandler\EncodingEventHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\ProcessEncodingResultHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\EncodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\FileDownloadService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\ProjectFileService;
use common\chip\externalIntegrations\alientech\Infrastructure\Configuration\ConfigProvider;
use common\chip\externalIntegrations\alientech\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\DecodingFacade;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\EncodingFacade;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AlientechHttpClient;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AuthProvider;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AuthProviderInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientFactory;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\DecodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\EncodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement\SlotManager;
use common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement\SlotManagerInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Testing\MockAlientechApiClient;

return [
    // ========================================
    // CORE INTERFACES
    // ========================================

    DecodingRepositoryInterface::class => DecodingRepository::class,
    EncodingRepositoryInterface::class => EncodingRepository::class,
    AlientechApiClientInterface::class => AlientechApiClient::class,

    // ========================================
    // DOMAIN SERVICES
    // ========================================

    ConfigProviderInterface::class => function () {
        return new ConfigProvider(require __DIR__ . '/alientech_api.php');
    },

    DecodingDomainService::class => function ($container) {
        return new DecodingDomainService(
            $container->get(DecodingRepositoryInterface::class)
        );
    },

    EncodingDomainService::class => function ($container) {
        return new EncodingDomainService(
            $container->get(EncodingRepositoryInterface::class)
        );
    },

    FileDownloadService::class => function ($container) {
        return new FileDownloadService(
            $container->get(AlientechHttpClient::class)
        );
    },

    ProjectFileService::class => function () {
        return new ProjectFileService();
    },

    // ========================================
    // INFRASTRUCTURE REPOSITORIES
    // ========================================

    DecodingRepository::class => function () {
        return new DecodingRepository();
    },

    EncodingRepository::class => function () {
        return new EncodingRepository();
    },

    // ========================================
    // INFRASTRUCTURE EXTERNAL SERVICES
    // ========================================

    AlientechApiClient::class => function ($container) {
        return new AlientechApiClient(
            $container->get(HttpClientInterface::class),
            $container->get(ConfigProviderInterface::class),
            $container->get(SlotManagerInterface::class)
        );
    },

    // ========================================
    // HTTP INFRASTRUCTURE
    // ========================================

    HttpClientFactory::class => function ($container) {
        return new HttpClientFactory(
            \Yii::$app->db,
            \Yii::$app->cache
        );
    },

    AlientechHttpClient::class => function () {
        return new AlientechHttpClient();
    },

    HttpClientInterface::class => function ($container) {
        $factory = $container->get(HttpClientFactory::class);
        $config = $container->get(ConfigProviderInterface::class);
        return $factory->createHttpClient($config);
    },

    AuthProviderInterface::class => function ($container) {
        return new AuthProvider(
            \Yii::$app->db,
            \Yii::$app->cache
        );
    },

    SlotManagerInterface::class => function ($container) {
        return new SlotManager(
            $container->get(HttpClientInterface::class),
            3
        );
    },

    // ========================================
    // APPLICATION HANDLERS
    // ========================================

    StartDecodingHandler::class => function ($container) {
        return new StartDecodingHandler(
            $container->get(DecodingDomainService::class),
            $container->get(DecodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    StartEncodingHandler::class => function ($container) {
        return new StartEncodingHandler(
            $container->get(EncodingDomainService::class),
            $container->get(EncodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    ProcessDecodingResultHandler::class => function ($container) {
        return new ProcessDecodingResultHandler(
            $container->get(DecodingRepositoryInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    ProcessEncodingResultHandler::class => function ($container) {
        return new ProcessEncodingResultHandler(
            $container->get(EncodingRepositoryInterface::class),
            $container->get(EventDispatcher::class),
            $container->get(EncodingCompletedEventHandler::class)
        );
    },

    EncodingCompletedEventHandler::class => function ($container) {
        return new EncodingCompletedEventHandler(
            $container->get(FileDownloadService::class),
            $container->get(ProjectFileService::class)
        );
    },

    DecodingEventHandler::class => function ($container) {
        return new DecodingEventHandler(
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    EncodingEventHandler::class => function ($container) {
        return new EncodingEventHandler(
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    // ========================================
    // LEGACY SERVICES (используем существующие)
    // ========================================

    AlientechService::class => function () {
        return new AlientechService();
    },

    EventDispatcher::class => function () {
        return Yii::$container->get(EventDispatcher::class);
    },

    // ========================================
    // FACADES (для удобства использования)
    // ========================================

    DecodingFacade::class => function ($container) {
        return new DecodingFacade(
            $container->get(StartDecodingHandler::class),
            $container->get(ProcessDecodingResultHandler::class)
        );
    },

    EncodingFacade::class => function ($container) {
        return new EncodingFacade(
            $container->get(StartEncodingHandler::class),
            $container->get(ProcessEncodingResultHandler::class)
        );
    },

    'alientech.decoding.facade' => function ($container) {
        return $container->get(DecodingFacade::class);
    },

    'alientech.encoding.facade' => function ($container) {
        return $container->get(EncodingFacade::class);
    },

    // ========================================
    // TESTING DEPENDENCIES
    // ========================================

    'alientech.test.decoding_repository' => function () {
        return new DecodingRepository();
    },

    'alientech.test.encoding_repository' => function () {
        return new EncodingRepository();
    },

    MockAlientechApiClient::class => function () {
        return new MockAlientechApiClient();
    },

    'alientech.test.mock_api_client' => function ($container) {
        return $container->get(MockAlientechApiClient::class);
    },
];
