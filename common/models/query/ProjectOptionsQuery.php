<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[ProjectOptions]].
 *
 * @see ProjectOptions
 */
class ProjectOptionsQuery extends BaseModelQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return ProjectOptions[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return ProjectOptions|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
