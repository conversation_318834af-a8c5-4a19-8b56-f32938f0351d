<?php

namespace common\models;

use common\models\query\ChipGearboxQuery;
use Yii;

/**
 * This is the model class for table "chip_gearbox".
 *
 * @property int $id
 * @property string $title
 * @property string $created_at
 * @property string $deleted_at
 */
class ChipGearbox extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_gearbox';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['created_at', 'deleted_at'], 'safe'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'created_at' => Yii::t('app', 'Created At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return ChipGearboxQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipGearboxQuery(get_called_class()));
    }
}
