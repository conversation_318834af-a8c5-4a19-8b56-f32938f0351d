<?php

namespace common\models;

use common\helpers\ProjectHelper;
use common\models\query\ProjectMessagesQuery;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Report\Xml\Project;
use Yii;

/**
 * This is the model class for table "project_messages".
 *
 * @property int $id
 * @property string $title
 * @property string $content
 * @property int $send_to
 * @property string $created_at Когда создан
 * @property string $updated_at Когда обновлен
 * @property string $deleted_at Когда удален
 * @property int $created_by Кем создан
 * @property int $deleted_by Кем удален
 * @property int $project_id Проект
 * @property int $isDeleted
 * @property string $comment Текст комментария
 */
class ProjectReport extends BaseChipModel
{
    public $path;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'project_note';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'project_id'], 'required'],
            [['created_at', 'updated_at', 'deleted_at', 'comment', 'sys', 'content', 'send_to'], 'safe'],
            [['created_by', 'updated_by', 'deleted_by', 'project_id', 'isDeleted', 'is_shown'], 'integer'],
            [['title', 'path', 'filename'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'content' => Yii::t('app', 'Content'),
            'created_at' => Yii::t('app', 'Когда создан'),
            'updated_at' => Yii::t('app', 'Когда обновлен'),
            'deleted_at' => Yii::t('app', 'Когда удален'),
            'created_by' => Yii::t('app', 'Кем создан'),
            'updated_by' => Yii::t('app', 'Кем обновлен'),
            'deleted_by' => Yii::t('app', 'Кем удален'),
            'project_id' => Yii::t('app', 'Проект'),
            'is_shown' => Yii::t('app', 'Is Shown'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'comment' => Yii::t('app', 'Комментарий'),
        ];
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCreator()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProject()
    {
        return $this->hasOne(Projects::className(), ['id' => 'project_id']);
    }
}
