<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "seo_chip_page".
 *
 * @property int $id
 * @property int $chip_ecu_id Chip ECU Id from chip_ecu
 * @property string $url ссылка - идентификатор
 * @property string $h1_ua Заголовок H1 укр
 * @property string $title_ua Название укр
 * @property string $keywords_ua Ключевые укр
 * @property string $description_ua Описание укр
 * @property string $seo_text_ua СЕО текст укр
 * @property string $h1_ru Заголовок H1 рус
 * @property string $title_ru Название рус
 * @property string $keywords_ru Ключевые рус
 * @property string $description_ru Описание рус
 * @property string $seo_text_ru СЕО текст рус
 * @property string $created_at
 * @property string $updated_at
 */
class SeoChipPage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'seo_chip_page';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['chip_ecu_id', 'url'], 'required'],
            [['chip_ecu_id'], 'integer'],
            [['seo_text_ua', 'seo_text_ru'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['url'], 'string', 'max' => 255],
            [['url'], 'unique'],
            [['h1_ua', 'title_ua', 'keywords_ua', 'description_ua', 'h1_ru', 'title_ru', 'keywords_ru', 'description_ru'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'chip_ecu_id' => Yii::t('app', 'Chip ECU Id from chip_ecu'),
            'url' => Yii::t('app', 'ссылка - идентификатор'),
            'h1_ua' => Yii::t('app', 'Заголовок H1 укр'),
            'title_ua' => Yii::t('app', 'Название укр'),
            'keywords_ua' => Yii::t('app', 'Ключевые укр'),
            'description_ua' => Yii::t('app', 'Описание укр'),
            'seo_text_ua' => Yii::t('app', 'СЕО текст укр'),
            'h1_ru' => Yii::t('app', 'Заголовок H1 рус'),
            'title_ru' => Yii::t('app', 'Название рус'),
            'keywords_ru' => Yii::t('app', 'Ключевые рус'),
            'description_ru' => Yii::t('app', 'Описание рус'),
            'seo_text_ru' => Yii::t('app', 'СЕО текст рус'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return SeoChipPageQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new SeoChipPageQuery(get_called_class());
    }
}
