<?php

namespace common\models;

use common\models\query\ChipVehicleQuery;
use Yii;

/**
 * This is the model class for table "chip_vehicle".
 *
 * @property int $id
 * @property string $title
 * @property int $isDefault
 * @property string $created_at
 * @property string $deleted_at
 * @property string $updated_at
 * @property int $deleted_by
 */
class ChipVehicle extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_vehicle';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['created_at', 'deleted_at', 'updated_at', 'isDefault'], 'safe'],
            [['deleted_by'], 'integer'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'title' => Yii::t('backend', 'Title'),
            'isDefault' => Yii::t('backend', 'isDefault'),
            'created_at' => Yii::t('backend', 'Created At'),
            'deleted_at' => Yii::t('backend', 'Deleted At'),
            'updated_at' => Yii::t('backend', 'Updated At'),
            'deleted_by' => Yii::t('backend', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return ChipVehicleQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipVehicleQuery(get_called_class()));
    }
}
