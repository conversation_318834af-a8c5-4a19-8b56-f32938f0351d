<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement;


use common\chip\externalIntegrations\alientech\Domain\Exception\ApiException;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\SlotInfo;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClient;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientInterface;

/**
 * Менеджер файловых слотов для Alientech API
 */
final class SlotManager implements SlotManagerInterface
{
    private const MAXIMUM_KESS_FILE_SLOTS = 3;

    public function __construct(
        private readonly HttpClient $httpClient,
        private readonly int $maxSlots = self::MAXIMUM_KESS_FILE_SLOTS
    ) {}
    
    public function hasAvailableSlots(): bool
    {
        if (YII_DEBUG) {
            return true;
        }
        try {
            $slots = $this->getSlots();
            $openSlots = $this->countOpenSlots($slots);

            $hasAvailable = $openSlots < $this->maxSlots;

            return $hasAvailable;

        } catch (\Throwable $e) {
            throw new ApiException('Failed to check available slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function getSlots(): array
    {
        try {
            // Запрашиваем слоты с API
            $request = new ApiRequest('GET', '/api/kess3/file-slots');
            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to get slots: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }

            $data = $response->getData();

            if (!is_array($data)) {
                $data = [];
            }

            // Преобразуем в SlotInfo объекты
            $slots = [];
            foreach ($data as $slotData) {
                if (is_object($slotData) || is_array($slotData)) {
                    $slotArray = is_object($slotData) ? (array) $slotData : $slotData;
                    $slots[] = SlotInfo::fromApiData($slotArray);
                }
            }

            return $slots;

        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw new ApiException('Failed to get slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function closeSlot(string $slotGuid): bool
    {
        if (empty($slotGuid)) {
            throw new \InvalidArgumentException('Slot GUID cannot be empty');
        }

        try {
            $url = '/api/kess3/file-slots/' . $slotGuid . '/close';
            $request = new ApiRequest('POST', $url);
            $request->addHeader('Content-Length', '0');

            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to close slot: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }

            return true;

        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw new ApiException('Failed to close slot: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function closeAllSlots(): array
    {
        try {
            $slots = $this->getSlots();
            $results = ['closed' => 0, 'failed' => 0, 'details' => []];

            foreach ($slots as $slot) {
                if ($slot->isOpen()) {
                    try {
                        $success = $this->closeSlot($slot->getGuid());

                        if ($success) {
                            $results['closed']++;
                            $results['details'][] = [
                                'guid' => $slot->getGuid(),
                                'status' => 'closed',
                                'ageInMinutes' => $slot->getAgeInMinutes()
                            ];
                        } else {
                            $results['failed']++;
                            $results['details'][] = [
                                'guid' => $slot->getGuid(),
                                'status' => 'failed',
                                'error' => 'Unknown error'
                            ];
                        }

                    } catch (\Throwable $e) {
                        $results['failed']++;
                        $results['details'][] = [
                            'guid' => $slot->getGuid(),
                            'status' => 'failed',
                            'error' => $e->getMessage()
                        ];
                    }
                }
            }

            return $results;

        } catch (\Throwable $e) {
            throw new ApiException('Failed to close all slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function reopenSlot(string $slotGuid): bool
    {
        if (empty($slotGuid)) {
            throw new \InvalidArgumentException('Slot GUID cannot be empty');
        }

        try {
            // Проверяем, есть ли доступные слоты
            if (!$this->hasAvailableSlots()) {
                $this->closeAllSlots();
            }

            $url = '/api/kess3/file-slots/' . $slotGuid . '/reopen';
            $request = new ApiRequest('POST', $url);
            $request->addHeader('Content-Length', '0');

            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to reopen slot: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }

            return true;

        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw new ApiException('Failed to reopen slot: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function getMaxSlots(): int
    {
        return $this->maxSlots;
    }
    
    public function getOpenSlotsCount(): int
    {
        $slots = $this->getSlots();
        return $this->countOpenSlots($slots);
    }

    /**
     * Подсчитывает количество открытых слотов
     */
    private function countOpenSlots(array $slots): int
    {
        $openSlots = 0;

        foreach ($slots as $slot) {
            if ($slot instanceof SlotInfo && $slot->isOpen()) {
                $openSlots++;
            }
        }

        return $openSlots;
    }
}
