<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Configuration;


use common\chip\externalIntegrations\alientech\Domain\Exception\ConfigurationException;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials;

/**
 * Провайдер конфигурации для Alientech API
 */
final readonly class ConfigProvider implements ConfigProviderInterface
{
    public function __construct(
        private ?array $config = []
    ) {}

    public function getCustomerCode(): string
    {
        $code = $this->config['customer_code'] ?? '';

        if (empty($code)) {
            throw ConfigurationException::missingKey('customer_code');
        }

        return $code;
    }

    public function getCallbackUrl(string $operationType): string
    {
        $callbacks = $this->config['callbacks'] ?? [];

        if (!isset($callbacks[$operationType])) {
            throw ConfigurationException::unknownOperationType($operationType);
        }

        return $callbacks[$operationType];
    }

    public function getApiEndpoint(string $endpoint): string
    {
        $endpoints = $this->config['endpoints'] ?? [];

        if (!isset($endpoints[$endpoint])) {
            throw ConfigurationException::unknownEndpoint($endpoint);
        }

        $url = $endpoints[$endpoint];

        // Заменяем плейсхолдеры
        if (str_contains($url, '{customerCode}')) {
            $url = str_replace('{customerCode}', $this->getCustomerCode(), $url);
        }

        return $url;
    }

    public function getApiBaseUrl(): string
    {
        $baseUrl = $this->config['base_url'] ?? '';

        if (empty($baseUrl)) {
            throw ConfigurationException::missingKey('base_url');
        }

        if (!filter_var($baseUrl, FILTER_VALIDATE_URL)) {
            throw ConfigurationException::invalidUrl('base_url', $baseUrl);
        }

        return rtrim($baseUrl, '/');
    }

    public function getAuthCredentials(): \common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials
    {
        $auth = $this->config['auth'] ?? [];

        $clientGuid = $auth['client_application_guid'] ?? '';
        $secretKey = $auth['secret_key'] ?? '';

        if (empty($clientGuid)) {
            throw ConfigurationException::missingKey('auth.client_application_guid');
        }

        if (empty($secretKey)) {
            throw ConfigurationException::missingKey('auth.secret_key');
        }

        return new AuthCredentials($clientGuid, $secretKey);
    }

    public function getTimeouts(): array
    {
        return $this->config['timeouts'] ?? [
            'default' => 30,
            'upload' => 120,
            'download' => 60,
            'auth' => 10,
            'slots' => 15,
        ];
    }

    public function getRetrySettings(): array
    {
        return $this->config['retry'] ?? [
            'max_attempts' => 3,
            'delay' => 1000,
            'backoff_multiplier' => 2.0,
        ];
    }

    /**
     * Возвращает настройки слотов
     */
    public function getSlotSettings(): array
    {
        return $this->config['slots'] ?? [
            'max_slots' => 3,
            'cache_duration' => 60,
            'auto_close_expired' => true,
            'expiry_minutes' => 30,
        ];
    }

    /**
     * Возвращает настройки логирования
     */
    public function getLoggingSettings(): array
    {
        return $this->config['logging'] ?? [
            'enabled' => true,
            'log_requests' => true,
            'log_responses' => true,
            'log_auth_details' => false,
        ];
    }

    /**
     * Возвращает настройки кеширования
     */
    public function getCacheSettings(): array
    {
        return $this->config['cache'] ?? [
            'enabled' => true,
            'prefix' => 'kess3_alientech_',
        ];
    }

    /**
     * Возвращает полный URL для endpoint'а
     */
    public function getFullApiUrl(string $endpoint): string
    {
        return $this->getApiBaseUrl() . $this->getApiEndpoint($endpoint);
    }

    /**
     * Возвращает таймаут для конкретного типа операции
     */
    public function getTimeout(string $operation): int
    {
        $timeouts = $this->getTimeouts();
        return $timeouts[$operation] ?? $timeouts['default'];
    }

    /**
     * Возвращает настройки аутентификации
     */
    public function getAuthSettings(): array
    {
        return $this->config['auth'] ?? [
            'token_cache_duration' => 3600,
            'token_refresh_threshold' => 300,
        ];
    }
}
