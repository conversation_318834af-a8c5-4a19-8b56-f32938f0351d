<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Http;

use Yii;
use yii\httpclient\Client;
use yii\httpclient\Exception;

/**
 * HTTP клиент для работы с Alientech API
 */
final class AlientechHttpClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * Выполнить GET запрос
     */
    public function get(string $url, array $headers = []): ?array
    {
        try {
            $request = $this->client->createRequest()
                ->setMethod('GET')
                ->setUrl($url);

            if (!empty($headers)) {
                $request->setHeaders($headers);
            }

            $response = $request->send();

            if (!$response->isOk) {
                Yii::warning(
                    message: "HTTP request failed with status {$response->statusCode}: {$response->content}",
                    category: 'alientech.http'
                );
                return null;
            }

            // Для файлов возвращаем сырые данные
            if ($this->isFileResponse($response)) {
                return [
                    'data' => $response->content,
                    'mimeType' => $response->headers->get('content-type', 'application/octet-stream')
                ];
            }

            // Для JSON ответов
            return $response->data;

        } catch (Exception $e) {
            Yii::error(
                message: "HTTP request failed: {$e->getMessage()}",
                category: 'alientech.http'
            );
            return null;
        }
    }

    /**
     * Выполнить POST запрос
     */
    public function post(string $url, array $data = [], array $headers = []): ?array
    {
        try {
            $request = $this->client->createRequest()
                ->setMethod('POST')
                ->setUrl($url)
                ->setData($data);

            if (!empty($headers)) {
                $request->setHeaders($headers);
            }

            $response = $request->send();

            if (!$response->isOk) {
                Yii::warning(
                    message: "HTTP POST request failed with status {$response->statusCode}: {$response->content}",
                    category: 'alientech.http'
                );
                return null;
            }

            return $response->data;

        } catch (Exception $e) {
            Yii::error(
                message: "HTTP POST request failed: {$e->getMessage()}",
                category: 'alientech.http'
            );
            return null;
        }
    }

    /**
     * Проверить, является ли ответ файлом
     */
    private function isFileResponse($response): bool
    {
        $contentType = $response->headers->get('content-type', '');
        
        // Проверяем, что это не JSON
        if (strpos($contentType, 'application/json') !== false) {
            return false;
        }

        // Проверяем, что это бинарные данные или файл
        return strpos($contentType, 'application/octet-stream') !== false ||
               strpos($contentType, 'application/') !== false ||
               strpos($contentType, 'binary/') !== false ||
               !empty($response->content);
    }
}
