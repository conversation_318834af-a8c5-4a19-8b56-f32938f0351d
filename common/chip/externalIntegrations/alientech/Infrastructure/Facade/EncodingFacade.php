<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Facade;

use common\chip\externalIntegrations\alientech\Application\Encoding\Command\ProcessEncodingResultCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Command\StartEncodingCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\ProcessEncodingResultHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;

/**
 * Фасад для операций кодирования
 */
final readonly class EncodingFacade
{
    public function __construct(
        private StartEncodingHandler $startHandler,
        private ProcessEncodingResultHandler $processResultHandler
    ) {
    }

    public function startEncoding(int $projectId, int $fileId, int $readMethodId, array $files, array $filePaths, ?string $callbackUrl = null): EncodingOperation
    {
        $command = new StartEncodingCommand(
            projectId: $projectId,
            fileId: $fileId,
            readMethodId: $readMethodId,
            files: $files,
            filePaths: $filePaths,
            callbackUrl: $callbackUrl
        );

        return $this->startHandler->handle($command);
    }

    /**
     * Обработать результат декодирования из callback
     */
    public function processEncodingResult(array $callbackData): ?EncodingOperation
    {
        $command = ProcessEncodingResultCommand::fromCallbackData($callbackData);
        return $this->processResultHandler->handle($command);
    }
}
