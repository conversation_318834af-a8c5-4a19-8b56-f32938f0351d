<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\EventHandler;

use common\chip\externalIntegrations\alientech\Application\Encoding\Event\EncodingCompletedEvent;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\FileDownloadService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\ProjectFileService;
use Yii;

/**
 * Обработчик события завершения энкодирования
 * Скачивает закодированный файл и сохраняет его в базу данных
 */
final readonly class EncodingCompletedEventHandler
{
    public function __construct(
        private FileDownloadService $fileDownloadService,
        private ProjectFileService $projectFileService
    ) {
    }

    public function handle(EncodingCompletedEvent $event): void
    {
        try {
            Yii::info(
                message: "Starting file download for encoding operation {$event->getOperationId()}",
                category: 'alientech.encoding.download'
            );

            $result = $event->getResult();
            
            // Проверяем наличие URL для скачивания
            if (!isset($result['EncodedFileURL']) || empty($result['EncodedFileURL'])) {
                Yii::warning(
                    message: "No encoded file URLs found in result for operation {$event->getOperationId()}",
                    category: 'alientech.encoding.download'
                );
                return;
            }

            $encodedFileUrl = $result['EncodedFileURL'];

            // Скачиваем и сохраняем файл
            $this->downloadAndSaveFile(
                event: $event,
                fileType: 'mod_enc',
                fileUrl: $encodedFileUrl
            );


            Yii::info(
                message: "File download completed for encoding operation {$event->getOperationId()}",
                category: 'alientech.encoding.download'
            );

        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to download encoded files for operation {$event->getOperationId()}: {$e->getMessage()}",
                category: 'alientech.encoding.download'
            );
            throw $e;
        }
    }

    private function downloadAndSaveFile(
        EncodingCompletedEvent $event,
        string $fileType,
        string $fileUrl
    ): void {
        try {
            // Скачиваем файл
            $fileData = $this->fileDownloadService->downloadFile($fileUrl);
            
            if (!$fileData) {
                Yii::warning(
                    message: "Failed to download file {$fileType} from {$fileUrl} for operation {$event->getOperationId()}",
                    category: 'alientech.encoding.download'
                );
                return;
            }

            // Сохраняем файл в базу данных
            $projectFile = $this->projectFileService->saveEncodedFile(
                projectId: $event->getProjectId(),
                files: $event->getFiles(),
                fileData: $fileData,
                fileType: $fileType,
                operationId: $event->getOperationId()
            );

            if ($projectFile) {
                Yii::info(
                    message: "Successfully saved encoded file {$fileType} for operation {$event->getOperationId()}, ProjectFile ID: {$projectFile->id}",
                    category: 'alientech.encoding.download'
                );
            } else {
                Yii::warning(
                    message: "Failed to save encoded file {$fileType} for operation {$event->getOperationId()}",
                    category: 'alientech.encoding.download'
                );
            }

        } catch (\Exception $e) {
            Yii::error(
                message: "Error processing file {$fileType} for operation {$event->getOperationId()}: {$e->getMessage()}",
                category: 'alientech.encoding.download'
            );
            throw $e;
        }
    }
}
