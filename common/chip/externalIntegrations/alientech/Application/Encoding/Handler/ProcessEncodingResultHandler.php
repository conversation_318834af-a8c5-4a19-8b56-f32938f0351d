<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Handler;

use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\alientech\Application\Encoding\Command\ProcessEncodingResultCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Event\EncodingCompletedEvent;
use common\chip\externalIntegrations\alientech\Application\Encoding\Event\EncodingFailedEvent;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use Yii;

/**
 * Обработчик команды обработки результата энкодирования
 */
final readonly class ProcessEncodingResultHandler
{
    public function __construct(
        private EncodingRepositoryInterface $repository,
        private EventDispatcher $eventDispatcher
    ) {
    }

    public function handle(ProcessEncodingResultCommand $command): EncodingOperation
    {
        $operation = $this->repository->findByExternalOperationId($command->externalOperationId);
        
        if (!$operation) {
            throw new \RuntimeException(
                "Encoding operation not found for external ID: {$command->externalOperationId}"
            );
        }

        if ($command->isCompleted) {
            if ($command->isSuccessful && $command->result) {
                $operation->complete($command->result);
                
                $event = new EncodingCompletedEvent(
                    operationId: $operation->getOperationId()->getValue(),
                    projectId: $operation->getProjectId()->getValue(),
                    fileIds: $operation->getFileIds(),
                    result: $command->result,
                    externalOperationId: $command->externalOperationId
                );
                
                $this->eventDispatcher->dispatchAsync(
                    event: $event,
                    entityType: 'kess3_encoding',
                    entityId: $operation->getProjectId()->getValue()
                );

                Yii::info(
                    message: "Encoding completed successfully for operation {$operation->getOperationId()}",
                    category: 'kess3.encoding'
                );
            } else {
                $errorData = $command->error ?? ['message' => 'Encoding failed without error details'];
                $operation->fail($errorData);
                
                $event = new EncodingFailedEvent(
                    operationId: $operation->getOperationId()->getValue(),
                    projectId: $operation->getProjectId()->getValue(),
                    fileIds: $operation->getFileIds(),
                    error: $errorData,
                    externalOperationId: $command->externalOperationId
                );
                
                $this->eventDispatcher->dispatchAsync(
                    event: $event,
                    entityType: 'kess3_encoding',
                    entityId: $operation->getProjectId()->getValue()
                );

                Yii::error(
                    message: "Encoding failed for operation {$operation->getOperationId()}: " . json_encode($errorData),
                    category: 'kess3.encoding'
                );
            }
        } else {
            // Обновляем промежуточный статус, если нужно
            Yii::info(
                message: "Received intermediate update for encoding operation {$operation->getOperationId()}",
                category: 'kess3.encoding'
            );
        }

        $this->repository->save($operation);
        
        return $operation;
    }
}
