<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Command;

/**
 * Команда обработки результата энкодирования
 */
final readonly class ProcessEncodingResultCommand
{
    public function __construct(
        public string $externalOperationId,
        public string $clientApplicationGuid,
        public int $asyncOperationType,
        public string $slotGuid,
        public int $status,
        public bool $isCompleted,
        public int $recommendedPollingInterval,
        public string $startedOn,
        public ?string $completedOn,
        public ?string $duration,
        public bool $isSuccessful,
        public bool $hasFailed,
        public ?array $result = null,
        public ?array $error = null,
        public ?array $additionalInfo = null,
        public ?array $userInfo = null,
        public ?string $callbackUrl = null,
        public ?string $jobRequestGuid = null
    ) {
    }

    public static function fromCallbackData(array $callbackData): self
    {
        return new self(
            externalOperationId: $callbackData['GUID'] ?? '',
            clientApplicationGuid: $callbackData['ClientApplicationGUID'] ?? '',
            asyncOperationType: (int) ($callbackData['AsyncOperationType'] ?? 0),
            slotGuid: $callbackData['SlotGUID'] ?? '',
            status: (int) ($callbackData['Status'] ?? 0),
            isCompleted: (bool) ($callbackData['IsCompleted'] ?? false),
            recommendedPollingInterval: (int) ($callbackData['RecommendedPollingInterval'] ?? 10),
            startedOn: $callbackData['StartedOn'] ?? '',
            completedOn: $callbackData['CompletedOn'] ?? null,
            duration: $callbackData['Duration'] ?? null,
            isSuccessful: (bool) ($callbackData['IsSuccessful'] ?? false),
            hasFailed: (bool) ($callbackData['HasFailed'] ?? false),
            result: $callbackData['Result'] ?? null,
            error: $callbackData['Error'] ?? null,
            additionalInfo: $callbackData['AdditionalInfo'] ?? null,
            userInfo: $callbackData['UserInfo'] ?? null,
            callbackUrl: $callbackData['CallbackURL'] ?? null,
            jobRequestGuid: $callbackData['JobRequestGUID'] ?? null
        );
    }

    /**
     * Получить GUID закодированного файла из результата
     */
    public function getEncodedFileGuid(): ?string
    {
        return $this->result['kess3FileGUID'] ?? null;
    }

    /**
     * Получить URL закодированного файла из результата
     */
    public function getEncodedFileUrl(): ?string
    {
        return $this->result['EncodedFileURL'] ?? null;
    }

    /**
     * Получить имя файла из результата
     */
    public function getFileName(): ?string
    {
        return $this->result['Name'] ?? null;
    }

    /**
     * Получить GUID слота файла из результата
     */
    public function getFileSlotGuid(): ?string
    {
        return $this->result['kess3FileSlotGUID'] ?? null;
    }

    /**
     * Получить GUID модифицированного EEPROM из дополнительной информации
     */
    public function getModifiedEepromGuid(): ?string
    {
        return $this->additionalInfo['ModifiedEEPROMGUID'] ?? null;
    }

    /**
     * Получить GUID модифицированного Flash из дополнительной информации
     */
    public function getModifiedFlashGuid(): ?string
    {
        return $this->additionalInfo['ModifiedFlashGUID'] ?? null;
    }

    /**
     * Получить GUID модифицированного Micro из дополнительной информации
     */
    public function getModifiedMicroGuid(): ?string
    {
        return $this->additionalInfo['ModifiedMicroGUID'] ?? null;
    }

    /**
     * Получить важность операции из дополнительной информации
     */
    public function getImportance(): float
    {
        return (float) ($this->additionalInfo['Importance'] ?? 0.0);
    }
}
