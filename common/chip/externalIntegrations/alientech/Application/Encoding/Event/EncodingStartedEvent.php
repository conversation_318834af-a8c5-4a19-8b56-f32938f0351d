<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие запуска операции кодирования
 */
final  class EncodingStartedEvent extends BaseEvent
{
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly array $fileIds,
        private readonly string $externalOperationId = '',
        private readonly string $slotGuid = '',
        private readonly string $service = 'alientech'
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_ids' => $fileIds,
            'external_operation_id' => $externalOperationId,
            'slot_guid' => $slotGuid,
            'service' => $service,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.encoding.started';
    }

    public function getDescription(): string
    {
        $filesCount = count($this->fileIds);
        return sprintf(
            'Encoding started for project %d, %d files (operation: %s, service: %s)',
            $this->projectId,
            $filesCount,
            $this->operationId,
            $this->service
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFiles(): array
    {
        return $this->fileIds;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function getSlotGuid(): string
    {
        return $this->slotGuid;
    }

    public function getService(): string
    {
        return $this->service;
    }
}
