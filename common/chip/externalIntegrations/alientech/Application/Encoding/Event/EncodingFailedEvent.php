<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие неудачного энкодирования
 */
final class EncodingFailedEvent extends BaseEvent
{
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly array $files,
        private readonly array $error,
        private readonly string $externalOperationId = ''
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_ids' => $files,
            'error' => $error,
            'external_operation_id' => $externalOperationId,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.encoding.failed';
    }

    public function getDescription(): string
    {
        $filesCount = count($this->files);
        $errorMessage = $this->error['message'] ?? 'Unknown error';
        return sprintf(
            'Encoding failed for project %d, %d files (operation: %s): %s',
            $this->projectId,
            $filesCount,
            $this->operationId,
            $errorMessage
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFiles(): array
    {
        return $this->files;
    }

    public function getError(): array
    {
        return $this->error;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function getErrorMessage(): string
    {
        return $this->error['message'] ?? 'Unknown error';
    }
}
