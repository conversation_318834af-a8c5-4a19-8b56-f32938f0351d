<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие запуска операции декодирования
 */
final  class DecodingStartedEvent extends BaseEvent
{
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly string $externalOperationId = '',
        private readonly string $slotGuid = ''
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_id' => $fileId,
            'external_operation_id' => $externalOperationId,
            'slot_guid' => $slotGuid,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.decoding.started';
    }

    public function getDescription(): string
    {
        return sprintf(
            'Decoding started for project %d, file %d (operation: %s)',
            $this->projectId,
            $this->fileId,
            $this->operationId
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFileId(): int
    {
        return $this->fileId;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function getSlotGuid(): string
    {
        return $this->slotGuid;
    }
}
