<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Shared;

/**
 * Value Object для типа чтения
 */
final readonly class ReadingType
{
    private const TYPE_OBD = 'obd';
    private const TYPE_BOOT = 'boot';
    
    private const VALID_TYPES = [
        self::TYPE_OBD,
        self::TYPE_BOOT,
    ];

    public function __construct(
        private string $value
    ) {
        if (!in_array($value, self::VALID_TYPES, true)) {
            throw new \InvalidArgumentException(
                sprintf('Invalid reading type: %s. Valid types: %s', $value, implode(', ', self::VALID_TYPES))
            );
        }
    }

    public static function obd(): self
    {
        return new self(self::TYPE_OBD);
    }

    public static function boot(): self
    {
        return new self(self::TYPE_BOOT);
    }

    /**
     * Создать из readmethod_id проекта
     */
    public static function fromReadMethodId(int $readMethodId): self
    {
        return match($readMethodId) {
            58 => self::obd(),    // KESS3 OBD
            59 => self::boot(),   // KESS3 BOOT
            default => throw new \InvalidArgumentException("Unsupported readmethod_id: {$readMethodId}")
        };
    }

    /**
     * Создать из readmethod_id проекта
     */
    public static function fromEncodingOperationType(int $operationType): self
    {
        return match($operationType) {
            6 => self::obd(),    // KESS3 OBD
            7 => self::boot(),   // KESS3 BOOT
            default => throw new \InvalidArgumentException("Unsupported readmethod_id: {$operationType}")
        };
    }

    /**
     * Получить тип операции кодирования для Alientech API
     */
    public function getEncodingOperationType(): int
    {
        return match($this->value) {
            self::TYPE_OBD => 6,  // KESS3_ENCODING_OBD
            self::TYPE_BOOT => 7, // KESS3_ENCODING_BOOT
        };
    }

    /**
     * Получить эндпоинт для кодирования
     */
    public function getEncodingEndpoint(): string
    {
        return match($this->value) {
            self::TYPE_OBD => '/api/kess3/encode-obd/{customerCode}',
            self::TYPE_BOOT => '/api/kess3/encode-boot/{customerCode}',
        };
    }

    /**
     * Получить callback URL для кодирования
     */
    public function getEncodingCallbackPath(): string
    {
        return match($this->value) {
            self::TYPE_OBD => '/api/kess3-encoded-obd',
            self::TYPE_BOOT => '/api/kess3-encoded-boot',
        };
    }

    /**
     * Получить допустимые типы файлов для кодирования
     */
    public function getValidFileTypes(): array
    {
        return match($this->value) {
            self::TYPE_OBD => [
                'OBDDecoded',
                'OBDModified',
            ],
            self::TYPE_BOOT => [
                'BootBenchDecodedMicro',
                'BootBenchDecodedFlash',
                'BootBenchDecodedEEPROM',
                'BootBenchDecodedMapFile',
                'BootBenchModifiedMicro',
                'BootBenchModifiedFlash',
                'BootBenchModifiedEEPROM',
                'BootBenchModifiedMapFile',
            ],
        };
    }

    /**
     * Получить ожидаемое время выполнения в секундах
     */
    public function getEstimatedDuration(): int
    {
        return match($this->value) {
            self::TYPE_OBD => 180,  // 3 минуты
            self::TYPE_BOOT => 240, // 4 минуты
        };
    }

    /**
     * Получить максимальное время ожидания в секундах
     */
    public function getTimeout(): int
    {
        return match($this->value) {
            self::TYPE_OBD => 1200, // 20 минут
            self::TYPE_BOOT => 1500, // 25 минут
        };
    }

    public function isObd(): bool
    {
        return $this->value === self::TYPE_OBD;
    }

    public function isBoot(): bool
    {
        return $this->value === self::TYPE_BOOT;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
