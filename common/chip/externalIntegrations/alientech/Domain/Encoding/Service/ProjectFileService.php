<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Encoding\Service;

use common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\FileData;
use common\helpers\ProjectHelper;
use common\models\ProjectFiles;
use common\models\Projects;
use Yii;

/**
 * Сервис для работы с файлами проекта
 */
final class ProjectFileService
{
    /**
     * Сохранить закодированный файл в базу данных
     */
    public function saveEncodedFile(
        int $projectId,
        array $files,
        FileData $fileData,
        string $fileType,
        string $operationId
    ): ?ProjectFiles {
        try {
            // Получаем проект
            $project = Projects::findOne($projectId);
            if (!$project) {
                Yii::warning(
                    message: "Project not found: {$projectId}",
                    category: 'alientech.file.save'
                );
                return null;
            }

            // Определяем оригинальный файл
            $originalFile = $this->getOriginalFile($files);
            if (!$originalFile) {
                Yii::warning(
                    message: "Original file not found for project {$projectId}",
                    category: 'alientech.file.save'
                );
                return null;
            }

            // Создаем путь для сохранения файла
            $filePath = $this->createFilePath($fileData, $project);
            
            // Сохраняем файл на диск
            if (!$this->saveFileToStorage($fileData, $filePath)) {
                return null;
            }

            // Создаем запись в базе данных
            $projectFile = $this->createProjectFileRecord(
                project: $project,
                originalFile: $originalFile,
                fileData: $fileData,
                filePath: $filePath,
                fileType: $fileType,
                operationId: $operationId
            );

            if ($projectFile->save(false)) {
                Yii::info(
                    message: "Encoded file saved successfully: {$projectFile->id}",
                    category: 'alientech.file.save'
                );
                return $projectFile;
            } else {
                Yii::error(
                    message: "Failed to save ProjectFile record: " . json_encode($projectFile->errors),
                    category: 'alientech.file.save'
                );
                return null;
            }

        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to save encoded file: {$e->getMessage()}",
                category: 'alientech.file.save'
            );
            return null;
        }
    }

    /**
     * Получить оригинальный файл из списка файлов
     */
    private function getOriginalFile(array $files): ?ProjectFiles
    {
        if (empty($files)) {
            return null;
        }

        // Берем первый файл как оригинальный
        $fileId = is_array($files[0]) ? $files[0]['id'] : $files[0];
        
        return ProjectFiles::findOne($fileId);
    }

    /**
     * Создать путь для сохранения файла
     */
    private function createFilePath(FileData $fileData, Projects $project): string
    {
        $storageDir = Yii::getAlias('@storage') . '/web/projects/files/';
        
        // Создаем имя файла
        $fileName = $this->generateFileName($fileData, $project);
        
        return $storageDir . $fileName;
    }

    /**
     * Сгенерировать имя файла
     */
    private function generateFileName(FileData $fileData, Projects $project): string
    {
        $baseName = $fileData->getFileName();
        
        // Если имя файла не содержит суффикс ENCODED, добавляем его
        if (strpos($baseName, '_ENCODED') === false) {
            $pathInfo = pathinfo($baseName);
            $name = $pathInfo['filename'] ?? $baseName;
            $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
            $baseName = $name . '_ENCODED' . $extension;
        }

        return $baseName;
    }

    /**
     * Сохранить файл в хранилище
     */
    private function saveFileToStorage(FileData $fileData, string $filePath): bool
    {
        try {
            // Создаем директорию если не существует
            $directory = dirname($filePath);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            // Декодируем данные если они в base64
            $data = $fileData->getData();
            if ($this->isBase64($data)) {
                $data = base64_decode($data);
            }

            $result = file_put_contents($filePath, $data);
            
            if ($result === false) {
                Yii::error(
                    message: "Failed to write file to: {$filePath}",
                    category: 'alientech.file.save'
                );
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Yii::error(
                message: "Error saving file to storage: {$e->getMessage()}",
                category: 'alientech.file.save'
            );
            return false;
        }
    }

    /**
     * Проверить, являются ли данные base64
     */
    private function isBase64(string $data): bool
    {
        return base64_encode(base64_decode($data, true)) === $data;
    }

    /**
     * Создать запись ProjectFile
     */
    private function createProjectFileRecord(
        Projects $project,
        ProjectFiles $originalFile,
        FileData $fileData,
        string $filePath,
        string $fileType,
        string $operationId
    ): ProjectFiles {
        $fileName = basename($filePath);
        
        $projectFile = new ProjectFiles();
        $projectFile->setAttributes([
            'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
            'type' => 'external',
            'title' => $fileName,
            'filename' => $fileName,
            'project_id' => $project->id,
            'file_id' => $originalFile->id,
            'alientech_operation_id' => $operationId,
            'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_ENCODED,
            'path' => $filePath,
            'hash' => Yii::$app->security->generateRandomString(12),
            'can_download' => 1,
        ]);

        return $projectFile;
    }
}
