<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Encoding\Service;

use common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\FileData;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AlientechHttpClient;
use Yii;

/**
 * Сервис для скачивания файлов с Alientech API
 */
final readonly class FileDownloadService
{
    public function __construct(
        private AlientechHttpClient $httpClient
    ) {
    }

    /**
     * Скачать файл по URL
     */
    public function downloadFile(string $url): ?FileData
    {
        try {
            Yii::info(
                message: "Downloading file from URL: {$url}",
                category: 'alientech.file.download'
            );

            if (empty($url)) {
                Yii::warning(
                    message: "Empty URL provided for file download",
                    category: 'alientech.file.download'
                );
                return null;
            }

            if (YII_DEBUG) {
                $fileContent = file_get_contents(Yii::getAlias('@storage') . "/payload/alientechEncodedBootBench.json");
                $response = json_decode($fileContent, true);
            } else {
                $response = $this->httpClient->get($url);
            }

            if (!$response || empty($response['data'])) {
                Yii::warning(
                    message: "No data received from URL: {$url}",
                    category: 'alientech.file.download'
                );
                return null;
            }

            return new FileData(
                data: $response['data'],
                fileName: $response['name'],
                mimeType: $response['mimeType'] ?? 'application/octet-stream'
            );

        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to download file from {$url}: {$e->getMessage()}",
                category: 'alientech.file.download'
            );
            return null;
        }
    }

    /**
     * Извлечь имя файла из URL
     */
    private function extractFileNameFromUrl(string $url): string
    {
        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'] ?? '';
        
        $fileName = basename($path);
        
        // Если не удалось извлечь имя файла, используем дефолтное
        if (empty($fileName) || $fileName === '/') {
            $fileName = 'encoded_file_' . time();
        }

        return $fileName;
    }
}
