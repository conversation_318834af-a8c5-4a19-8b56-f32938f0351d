<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject;

/**
 * Value Object для данных файла
 */
final readonly class FileData
{
    public function __construct(
        private string $data,
        private string $fileName,
        private string $mimeType = 'application/octet-stream'
    ) {
    }

    public function getData(): string
    {
        return $this->data;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }

    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    public function getSize(): int
    {
        return strlen($this->data);
    }

    public function isEmpty(): bool
    {
        return empty($this->data);
    }
}
