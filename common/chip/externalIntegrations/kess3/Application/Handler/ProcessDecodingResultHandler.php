<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Handler;

use common\chip\externalIntegrations\kess3\Application\Command\ProcessDecodingResultCommand;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingFailedEvent;
use common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\DecodingOperationRepositoryInterface;
use common\chip\event\EventDispatcher;
use Yii;

/**
 * Обработчик команды обработки результата декодирования из callback
 */
final readonly class ProcessDecodingResultHandler
{
    public function __construct(
        private DecodingOperationRepositoryInterface $repository,
        private EventDispatcher $eventDispatcher
    ) {
    }

    public function handle(ProcessDecodingResultCommand $command): ?DecodingOperation
    {
        try {
            // Находим операцию по внешнему ID
            $operation = $this->repository->findByExternalId($command->externalOperationId);
            
            if ($operation === null) {
                Yii::warning(
                    message: "Operation not found for external ID: {$command->externalOperationId}",
                    category: 'kess3.decoding'
                );
                return null;
            }

            // Проверяем, что операция может быть завершена
            if (!$command->isCompleted) {
                Yii::info(
                    message: "Operation {$operation->getOperationId()} is not completed yet",
                    category: 'kess3.decoding'
                );
                return $operation;
            }

            // Обрабатываем результат в зависимости от успешности
            if ($command->isSuccessful && $command->result !== null) {
                $this->handleSuccessfulCompletion($operation, $command);
            } else {
                $this->handleFailedCompletion($operation, $command);
            }

            // Сохраняем обновленную операцию
            $this->repository->save($operation);

            return $operation;
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to process decoding result: {$e->getMessage()}",
                category: 'kess3.decoding'
            );

            throw new \RuntimeException(
                message: "Failed to process decoding result: {$e->getMessage()}",
                previous: $e
            );
        }
    }

    private function handleSuccessfulCompletion(
        DecodingOperation $operation,
        ProcessDecodingResultCommand $command
    ): void {
        // Завершаем операцию с результатом
        $operation->complete($command->result ?? []);

        // Отправляем событие успешного завершения
        $event = new DecodingCompletedEvent(
            operationId: $operation->getOperationId()->getValue(),
            projectId: $operation->getProjectId()->getValue(),
            fileId: $operation->getFileId()->getValue(),
            externalOperationId: $operation->getExternalOperationId() ?? '',
            result: $operation->getResult() ?? []
        );

        $this->eventDispatcher->dispatch(
            event: $event,
            entityType: 'kess3_decoding',
            entityId: $operation->getFileId()->getValue()
        );

        Yii::info(
            message: "Decoding completed successfully for operation {$operation->getOperationId()}",
            category: 'kess3.decoding'
        );
    }

    private function handleFailedCompletion(
        DecodingOperation $operation,
        ProcessDecodingResultCommand $command
    ): void {
        // Завершаем операцию с ошибкой
        $errorData = $command->error ?? ['message' => 'Unknown error'];
        $operation->fail($errorData);

        // Отправляем событие ошибки
        $errorMessage = is_array($errorData) && isset($errorData['message']) 
            ? $errorData['message'] 
            : 'Decoding failed';

        $event = new DecodingFailedEvent(
            operationId: $operation->getOperationId()->getValue(),
            projectId: $operation->getProjectId()->getValue(),
            fileId: $operation->getFileId()->getValue(),
            externalOperationId: $operation->getExternalOperationId() ?? '',
            error: $errorData,
            errorMessage: $errorMessage
        );

        $this->eventDispatcher->dispatchAsync(
            event: $event,
            entityType: 'kess3_decoding',
            entityId: $operation->getFileId()->getValue()
        );

        Yii::error(
            message: "Decoding failed for operation {$operation->getOperationId()}: {$errorMessage}",
            category: 'kess3.decoding'
        );
    }
}
