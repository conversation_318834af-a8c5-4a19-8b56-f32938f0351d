<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Http;

use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use Psr\Log\LoggerInterface;
use yii\db\Connection;
use yii\caching\CacheInterface;

/**
 * Фабрика для создания HTTP компонентов
 */
final readonly class HttpClientFactory
{
    public function __construct(
        private Connection $db,
        private CacheInterface $cache,
        private LoggerInterface $logger
    ) {}

    /**
     * Создает настроенный HTTP клиент с аутентификацией
     */
    public function createHttpClient(ConfigProviderInterface $config): HttpClientInterface
    {
        // Создаем провайдер аутентификации
        $authProvider = $this->createAuthProvider($config);

        // Создаем HTTP клиент
        $httpClient = new HttpClient($this->logger);
        $httpClient->setBaseUrl($config->getApiBaseUrl());
        $httpClient->setDefaultTimeout($config->getTimeout('default'));
        $httpClient->setAuthProvider($authProvider);

        return $httpClient;
    }

    /**
     * Создает провайдер аутентификации
     */
    public function createAuthProvider(ConfigProviderInterface $config): AuthProviderInterface
    {
        $authSettings = $config->getAuthSettings();

        return new AuthProvider(
            db: $this->db,
            cache: $this->cache,
            logger: $this->logger,
            authUrl: $config->getApiEndpoint('auth'),
            tokenCacheDuration: $authSettings['token_cache_duration'] ?? 3600,
            refreshThreshold: $authSettings['token_refresh_threshold'] ?? 300
        );
    }

    /**
     * Создает простой HTTP клиент без аутентификации (для публичных API)
     */
    public function createSimpleHttpClient(string $baseUrl, int $timeout = 30): HttpClientInterface
    {
        $httpClient = new HttpClient($this->logger);
        $httpClient->setBaseUrl($baseUrl);
        $httpClient->setDefaultTimeout($timeout);

        return $httpClient;
    }
}
