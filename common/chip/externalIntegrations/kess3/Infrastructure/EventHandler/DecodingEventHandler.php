<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\EventHandler;

use common\chip\event\core\EventInterface;
use common\chip\event\EventHandler;
use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingFailedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\alientech\entities\dto\AsyncOperationResultDto;
use common\chip\event\core\ProjectNoteAddedEvent;
use common\chip\event\core\ProjectStatusChangedEvent;
use common\chip\event\core\FileProcessedEvent;
use common\chip\event\core\FileProcessingErrorEvent;
use common\models\ProjectFiles;
use common\models\Projects;
use common\helpers\ProjectHelper;
use common\helpers\MessageHelper;
use Yii;

/**
 * Обработчик событий декодирования для интеграции с системой событий проекта
 */
final readonly class DecodingEventHandler implements EventHandler
{
    public function __construct(
        private AlientechApiClient $apiClient,
        private EventDispatcher $eventDispatcher
    ) {
    }

    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof DecodingStartedEvent
            || $event instanceof DecodingCompletedEvent
            || $event instanceof DecodingFailedEvent;
    }

    public function handle(EventInterface $event): void
    {
        try {
            if ($event instanceof DecodingStartedEvent) {
                $this->handleDecodingStarted($event);
            } elseif ($event instanceof DecodingCompletedEvent) {
                $this->handleDecodingCompleted($event);
            } elseif ($event instanceof DecodingFailedEvent) {
                $this->handleDecodingFailed($event);
            }
        } catch (\Exception $e) {
            Yii::error(
                message: "Error handling decoding event: {$e->getMessage()}",
                category: 'kess3.event_handler'
            );

            // Отправляем событие ошибки обработки файла
            if ($event instanceof DecodingStartedEvent || $event instanceof DecodingCompletedEvent || $event instanceof DecodingFailedEvent) {
                $this->sendFileProcessingErrorEvent(
                    $event->getProjectId(),
                    $event->getFileId(),
                    'kess3_decoding',
                    "Event handler error: {$e->getMessage()}"
                );
            }
        }
    }

    private function handleDecodingStarted(DecodingStartedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        // Отправляем событие добавления заметки о начале декодирования
        $noteEvent = new ProjectNoteAddedEvent(
            projectId: $event->getProjectId(),
            noteId: 0, // Временное значение, будет установлено позже
            userId: MessageHelper::ALIENTECH_USER_ID ?? 0,
            title: 'Kess3 Decoding Started',
            userRole: 'system',
            projectAuthorId: $project->created_by,
            noteData: [
                'operation_id' => $event->getOperationId(),
                'external_operation_id' => $event->getExternalOperationId(),
                'note_content' => "Decoding process started for operation {$event->getOperationId()}",
                'note_comment' => "External operation ID: {$event->getExternalOperationId()}",
                'note_type' => 'system_alientech'
            ]
        );

        $this->eventDispatcher->dispatch(
            $noteEvent,
            'project',
            $project->id,
            MessageHelper::ALIENTECH_USER_ID ?? 0
        );

        Yii::info(
            message: "Decoding started event handled for project {$event->getProjectId()}",
            category: 'kess3.event_handler'
        );
    }

    private function handleDecodingCompleted(DecodingCompletedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        try {
            // Скачиваем декодированные файлы
            $this->downloadDecodedFiles($event);

            // Отправляем событие добавления заметки об успешном завершении
            $noteEvent = new ProjectNoteAddedEvent(
                projectId: $event->getProjectId(),
                noteId: 0,
                userId: MessageHelper::ALIENTECH_USER_ID ?? 0,
                title: 'Kess3 Decoding Completed',
                userRole: 'system',
                projectAuthorId: $project->created_by,
                noteData: [
                    'operation_id' => $event->getOperationId(),
                    'external_operation_id' => $event->getExternalOperationId(),
                    'note_content' => "Decoding completed successfully for operation {$event->getOperationId()}",
                    'note_comment' => 'Decoded files have been downloaded and added to the project',
                    'note_type' => 'system_alientech'
                ]
            );

            $this->eventDispatcher->dispatchAsync(
                $noteEvent,
                'project',
                $project->id,
                MessageHelper::ALIENTECH_USER_ID ?? 0
            );

            // Отправляем событие успешной обработки файла
            $fileProcessedEvent = new FileProcessedEvent(
                projectId: $event->getProjectId(),
                sourceFileId: $event->getFileId(),
                processedFileId: $event->getFileId(), // Используем тот же ID, так как создаются новые файлы
                processInfo: [
                    'operation_type' => 'kess3_decoding',
                    'operation_id' => $event->getOperationId(),
                    'external_operation_id' => $event->getExternalOperationId(),
                    'result_data' => $event->getResult()
                ]
            );

            $this->eventDispatcher->dispatchAsync(
                $fileProcessedEvent,
                'project',
                $project->id,
                MessageHelper::ALIENTECH_USER_ID ?? 0
            );

            // Обновляем статус проекта и отправляем событие изменения статуса
            $oldStatus = $project->status_admin;
            $this->updateProjectStatus($project, ProjectHelper::STATUS_CHANGED);

            $statusChangedEvent = new ProjectStatusChangedEvent(
                projectId: $event->getProjectId(),
                oldStatus: $oldStatus,
                newStatus: ProjectHelper::STATUS_CHANGED
            );

            $this->eventDispatcher->dispatchAsync(
                $statusChangedEvent,
                'project',
                $project->id,
                $project->created_by
            );

            Yii::info(
                message: "Decoding completed event handled for project {$event->getProjectId()}",
                category: 'kess3.event_handler'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Error processing completed decoding: {$e->getMessage()}",
                category: 'kess3.event_handler'
            );

            $this->handleDecodingError($event->getProjectId(), $event->getFileId(), $e->getMessage());
        }
    }

    private function handleDecodingFailed(DecodingFailedEvent $event): void
    {
        $project = $this->getProject($event->getProjectId());
        if (!$project) {
            return;
        }

        // Отправляем событие добавления заметки об ошибке
        $noteEvent = new ProjectNoteAddedEvent(
            projectId: $event->getProjectId(),
            noteId: 0,
            userId: MessageHelper::ALIENTECH_USER_ID ?? 0,
            title: 'Kess3 Decoding Failed',
            userRole: 'system',
            projectAuthorId: $project->created_by,
            noteData: [
                'operation_id' => $event->getOperationId(),
                'external_operation_id' => $event->getExternalOperationId(),
                'note_content' => "Decoding failed for operation {$event->getOperationId()}: {$event->getErrorMessage()}",
                'note_comment' => json_encode($event->getError()),
                'note_type' => 'system_alientech'
            ]
        );

        $this->eventDispatcher->dispatchAsync(
            $noteEvent,
            'project',
            $project->id,
            MessageHelper::ALIENTECH_USER_ID ?? 0
        );

        // Отправляем событие ошибки обработки файла
        $this->sendFileProcessingErrorEvent(
            $event->getProjectId(),
            $event->getFileId(),
            'kess3_decoding',
            $event->getErrorMessage()
        );

        // Обновляем статус проекта и отправляем событие изменения статуса
        $oldStatus = $project->status_admin;
        $this->updateProjectStatus($project, ProjectHelper::STATUS_ERROR);

        $statusChangedEvent = new ProjectStatusChangedEvent(
            projectId: $event->getProjectId(),
            oldStatus: $oldStatus,
            newStatus: ProjectHelper::STATUS_ERROR
        );

        $this->eventDispatcher->dispatchAsync(
            $statusChangedEvent,
            'project',
            $project->id,
            $project->created_by
        );

        Yii::error(
            message: "Decoding failed for project {$event->getProjectId()}: {$event->getErrorMessage()}",
            category: 'kess3.event_handler'
        );
    }

    private function downloadDecodedFiles(DecodingCompletedEvent $event): void
    {
        $result = $event->getResult();

        if (empty($result)) {
            Yii::warning(
                message: "No decoded file URLs found in result for operation {$event->getOperationId()}",
                category: 'kess3.event_handler'
            );
            return;
        }

        $operationResultDto = new AsyncOperationResultDto((object) $result);
        $urls = $operationResultDto->generateDecodedFileUrls();

        foreach ($urls as $type => $url) {
            try {
                if (YII_DEBUG) {
                    Yii::info(
                        message: "Downloading decoded file from {$url}",
                        category: 'kess3.event_handler'
                    );

                    $fileContent = file_get_contents(Yii::getAlias('@storage') . "/payload/alientechDecoded{$type}.json");
                    $fileData = json_decode($fileContent, true);
                } else {
                    $fileData = $this->apiClient->downloadFile($url);
                }

                if (empty($fileData['data'])) {
                    continue;
                }

                $this->saveDecodedFile($event, $fileData, $type);
            } catch (\Exception $e) {
                Yii::error(
                    message: "Failed to download decoded file from {$url}: {$e->getMessage()}",
                    category: 'kess3.event_handler'
                );
                
                // Отправляем событие ошибки загрузки файла
                $this->sendFileProcessingErrorEvent(
                    $event->getProjectId(),
                    $event->getFileId(),
                    'kess3_file_download',
                    "Failed to download decoded file: {$e->getMessage()}"
                );
            }
        }
    }

    private function saveDecodedFile(DecodingCompletedEvent $event, array $fileData, string $type): void
    {
        $fileName = $this->generateDecodedFileName($event, $type);
        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;

        // Декодируем и сохраняем файл
        $decodedData = base64_decode($fileData['data']);
        
        if (file_put_contents($filePath, $decodedData) === false) {
            throw new \RuntimeException("Failed to save decoded file: {$fileName}");
        }
        $fileData['data'] = '';
        
        // Создаем запись в БД
        $projectFile = new ProjectFiles([
            'type' => 'external',
            'title' => $fileName,
            'project_id' => $event->getProjectId(),
            'file_id' => $event->getFileId(),
            'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
            'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
            'params' => json_encode($fileData),
            'path' => $filePath,
            'filename' => $fileName,
            'hash' => Yii::$app->security->generateRandomString(12),
        ]);

        if (!$projectFile->save()) {
            Yii::error(
                message: "Failed to save project file record: " . json_encode($projectFile->errors),
                category: 'kess3.event_handler'
            );
        }
    }

    private function generateDecodedFileName(DecodingCompletedEvent $event, string $type): string
    {
        $suffix = $type ? "_DECODED_{$type}" : '_DECODED';
        return "project_{$event->getProjectId()}_file_{$event->getFileId()}{$suffix}.bin";
    }

    private function getProject(int $projectId): ?Projects
    {
        return Projects::findOne($projectId);
    }

    private function updateProjectStatus(Projects $project, int $status): void
    {
        $project->status_admin = $status;
        $project->updated_by = $project->created_by;
        $project->save(false);
    }

    private function handleDecodingError(int $projectId, int $fileId, string $errorMessage): void
    {
        $project = $this->getProject($projectId);
        if ($project) {
            $oldStatus = $project->status_admin;
            $this->updateProjectStatus($project, ProjectHelper::STATUS_ERROR);

            // Отправляем событие изменения статуса проекта
            $statusChangedEvent = new ProjectStatusChangedEvent(
                projectId: $projectId,
                oldStatus: $oldStatus,
                newStatus: ProjectHelper::STATUS_ERROR
            );

            $this->eventDispatcher->dispatchAsync(
                $statusChangedEvent,
                'project',
                $project->id,
                $project->created_by
            );

            // Отправляем событие добавления заметки об ошибке
            $noteEvent = new ProjectNoteAddedEvent(
                projectId: $projectId,
                noteId: 0,
                userId: MessageHelper::ALIENTECH_USER_ID ?? 0,
                title: 'Kess3 Decoding Error',
                userRole: 'system',
                projectAuthorId: $project->created_by,
                noteData: [
                    'note_content' => "Error processing decoded files: {$errorMessage}",
                    'note_comment' => $errorMessage,
                    'note_type' => 'system_alientech'
                ]
            );

            $this->eventDispatcher->dispatchAsync(
                $noteEvent,
                'project',
                $project->id,
                MessageHelper::ALIENTECH_USER_ID ?? 0
            );
        }

        // Отправляем событие ошибки обработки файла
        $this->sendFileProcessingErrorEvent(
            $projectId,
            $fileId,
            'kess3_file_processing',
            $errorMessage
        );
    }

    /**
     * Отправляет событие ошибки обработки файла
     */
    private function sendFileProcessingErrorEvent(
        int $projectId,
        int $fileId,
        string $operation,
        string $errorMessage
    ): void {
        try {
            $errorEvent = new FileProcessingErrorEvent(
                projectId: $projectId,
                fileId: $fileId,
                operation: $operation,
                errorMessage: $errorMessage
            );

            $this->eventDispatcher->dispatchAsync(
                $errorEvent,
                'project',
                $projectId,
                MessageHelper::ALIENTECH_USER_ID ?? 0
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to send file processing error event: {$e->getMessage()}",
                category: 'kess3.event_handler'
            );
        }
    }
}
