<?php

use yii\db\Migration;

/**
 * Миграция для расширения таблицы alientech_async_operation для новой архитектуры декодирования
 */
class m240526_120000_extend_alientech_async_operation_for_new_architecture extends Migration
{
    /**
     * {@inheritDoc}
     */
    public function safeUp()
    {
        // Добавляем новые поля для поддержки новой архитектуры
        $this->addColumn('{{%alientech_async_operation}}', 'file_path', $this->string(500)->null()->comment('Путь к файлу для декодирования'));
        $this->addColumn('{{%alientech_async_operation}}', 'callback_url', $this->string(500)->null()->comment('URL для callback'));
        $this->addColumn('{{%alientech_async_operation}}', 'external_guid', $this->string(100)->null()->comment('Внешний GUID операции от Alientech'));

        // Добавляем индексы для производительности
        $this->createIndex('idx_alientech_async_operation_external_guid', '{{%alientech_async_operation}}', 'external_guid');
        $this->createIndex('idx_alientech_async_operation_status', '{{%alientech_async_operation}}', 'status');
        $this->createIndex('idx_alientech_async_operation_project_file', '{{%alientech_async_operation}}', ['project_id', 'file_id']);

        // Создаем индекс для быстрого поиска активных операций
        $this->createIndex(
            'idx_alientech_async_operation_active',
            '{{%alientech_async_operation}}',
            ['status', 'isCompleted', 'created_at']
        );

        echo "Extended alientech_async_operation table for new architecture.\n";
    }

    /**
     * {@inheritDoc}
     */
    public function safeDown()
    {
        // Удаляем индексы
        $this->dropIndex('idx_alientech_async_operation_active', '{{%alientech_async_operation}}');
        $this->dropIndex('idx_alientech_async_operation_project_file', '{{%alientech_async_operation}}');
        $this->dropIndex('idx_alientech_async_operation_status', '{{%alientech_async_operation}}');
        $this->dropIndex('idx_alientech_async_operation_external_guid', '{{%alientech_async_operation}}');

        // Удаляем новые поля
        $this->dropColumn('{{%alientech_async_operation}}', 'external_guid');
        $this->dropColumn('{{%alientech_async_operation}}', 'callback_url');
        $this->dropColumn('{{%alientech_async_operation}}', 'file_path');

        echo "Reverted alientech_async_operation table changes.\n";
    }
}
