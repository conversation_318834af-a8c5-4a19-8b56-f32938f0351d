[1] Log opened at 2025-05-28 18:50:04.249455
[1] [Step Debug] INFO: Connecting to configured address/port: host.docker.internal:9003.
[1] [Step Debug] INFO: Connected to debugging client: host.docker.internal:9003 (through xdebug.client_host/xdebug.client_port).
[1] [Step Debug] -> <init xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" fileuri="file:///app/vendor/phpunit/phpunit/phpunit" language="PHP" xdebug:language_version="8.2.28" protocol_version="1.0" appid="1" idekey="PHPSTORM"><engine version="3.3.0alpha3"><![CDATA[Xdebug]]></engine><author><![CDATA[Der<PERSON>]]></author><url><![CDATA[https://xdebug.org]]></url><copyright><![CDATA[Copyright (c) 2002-2023 by <PERSON><PERSON>]]></copyright></init>

[1] [Step Debug] <- feature_set -i 1 -n show_hidden -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="1" feature="show_hidden" success="1"></response>

[1] [Step Debug] <- feature_set -i 2 -n max_depth -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="2" feature="max_depth" success="1"></response>

[1] [Step Debug] <- feature_set -i 3 -n max_children -v 100
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="3" feature="max_children" success="1"></response>

[1] [Step Debug] <- feature_set -i 4 -n extended_properties -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="4" feature="extended_properties" success="1"></response>

[1] [Step Debug] <- feature_set -i 5 -n notify_ok -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="5" feature="notify_ok" success="1"></response>

[1] [Step Debug] <- feature_set -i 6 -n resolved_breakpoints -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="6" feature="resolved_breakpoints" success="1"></response>

[1] [Step Debug] <- feature_set -i 7 -n breakpoint_include_return_value -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="7" feature="breakpoint_include_return_value" success="1"></response>

[1] [Step Debug] <- stdout -i 8 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stdout" transaction_id="8" success="1"></response>

[1] [Step Debug] <- status -i 9
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="status" transaction_id="9" status="starting" reason="ok"></response>

[1] [Step Debug] <- step_into -i 10
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="step_into" transaction_id="10" status="break" reason="ok"><xdebug:message filename="file:///app/vendor/phpunit/phpunit/phpunit" lineno="12"></xdebug:message></response>

[1] [Step Debug] <- eval -i 11 -- Z2V0ZW52KCdQSFBfSURFX0NPTkZJRycpIT1mYWxzZQ==
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="11"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 12 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9OQU1FJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="12"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 13 -- aXNzZXQoJF9TRVJWRVJbJ1NTSF9DT05ORUNUSU9OJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="13"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 14 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9BRERSJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="14"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- breakpoint_set -i 15 -t line -f file://D:/000/chiptuning/common/services/TelegramApiService.php -n 132
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/services/TelegramApiService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="15" id="10001" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 16 -t line -f file://D:/000/chiptuning/common/chip/externalIntegrations/kess3/Application/Handler/ProcessDecodingResultHandler.php -n 81
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/externalIntegrations/kess3/Application/Handler/ProcessDecodingResultHandler.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="16" id="10002" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 17 -t line -f file://D:/000/chiptuning/common/chip/externalIntegrations/kess3/Application/Service/Kess3DecodingFacade.php -n 55
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/externalIntegrations/kess3/Application/Service/Kess3DecodingFacade.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="17" id="10003" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 18 -t line -f file://D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php -n 184
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="18" id="10004" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 19 -t line -f file://D:/000/chiptuning/common/chip/notification/Job/ChannelDeliveryJob.php -n 95
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/notification/Job/ChannelDeliveryJob.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="19" id="10005" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 20 -t line -f file://D:/000/chiptuning/common/chip/externalIntegrations/kess3/Infrastructure/EventHandler/DecodingEventHandler.php -n 181
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/externalIntegrations/kess3/Infrastructure/EventHandler/DecodingEventHandler.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="20" id="10006" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 21 -t line -f file://D:/000/chiptuning/common/chip/alientech/services/Kess3Service.php -n 66
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/services/Kess3Service.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="21" id="10007" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 22 -t line -f file://D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php -n 24
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="22" id="10008" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 23 -t line -f file://D:/000/chiptuning/common/chip/project/services/CreateProjectService.php -n 56
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/project/services/CreateProjectService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="23" id="10009" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 24 -t line -f file://D:/000/chiptuning/common/chip/project/services/ProjectService.php -n 621
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/project/services/ProjectService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="24" id="10010" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 25 -t line -f file://D:/000/chiptuning/common/services/TelegramApiService.php -n 123
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/services/TelegramApiService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="25" id="10011" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 26 -t line -f file://D:/000/chiptuning/tests/integration/RetryMechanismTest.php -n 115
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/tests/integration/RetryMechanismTest.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="26" id="10012" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 27 -t line -f file://D:/000/chiptuning/backend/controllers/ProjectsController.php -n 387
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/backend/controllers/ProjectsController.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="27" id="10013" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 28 -t line -f file://D:/000/chiptuning/common/services/TelegramApiService.php -n 157
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/services/TelegramApiService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="28" id="10014" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 29 -t line -f file://D:/000/chiptuning/common/chip/notification/channel/TelegramChannel.php -n 82
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/notification/channel/TelegramChannel.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="29" id="10015" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 30 -t line -f file://D:/000/chiptuning/common/chip/project/services/CreateProjectService.php -n 60
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/project/services/CreateProjectService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="30" id="10016" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 31 -t line -f file://D:/000/chiptuning/common/chip/event/routing/resolver/DefaultChannelSelector.php -n 249
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/event/routing/resolver/DefaultChannelSelector.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="31" id="10017" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 32 -t line -f file://D:/000/chiptuning/tests/integration/RetryMechanismTest.php -n 112
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/tests/integration/RetryMechanismTest.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="32" id="10018" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 33 -t line -f file://D:/000/chiptuning/common/chip/event/routing/resolver/RecipientResolverInterface.php -n 19
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/event/routing/resolver/RecipientResolverInterface.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="33" id="10019" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 34 -t line -f file://D:/000/chiptuning/backend/modules/ticket/controllers/AdminController.php -n 88
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/backend/modules/ticket/controllers/AdminController.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="34" id="10020" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 35 -t line -f file://D:/000/chiptuning/common/chip/notification/channel/TelegramChannel.php -n 108
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/notification/channel/TelegramChannel.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="35" id="10021" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 36 -t line -f file://D:/000/chiptuning/common/chip/project/services/ProjectService.php -n 618
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/project/services/ProjectService.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="36" id="10022" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 37 -t line -f file://D:/000/chiptuning/common/chip/externalIntegrations/kess3/Domain/Repository/DecodingOperationRepositoryInterface.php -n 20
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/externalIntegrations/kess3/Domain/Repository/DecodingOperationRepositoryInterface.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="37" id="10023" resolved="unresolved"></response>

[1] [Step Debug] <- stack_get -i 38
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="38"><stack where="{main}" level="0" type="file" filename="file:///app/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- stack_get -i 39
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="39"><stack where="{main}" level="0" type="file" filename="file:///app/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- context_names -i 40
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_names" transaction_id="40"><context name="Locals" id="0"></context><context name="Superglobals" id="1"></context><context name="User defined constants" id="2"></context></response>

[1] [Step Debug] <- context_get -i 41 -d 0 -c 0
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="41" context="0"><property name="$file" fullname="$file" type="uninitialized"></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 42 -d 0 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="42" context="1"><property name="$_GET" fullname="$_GET" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_POST" fullname="$_POST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_COOKIE" fullname="$_COOKIE" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_FILES" fullname="$_FILES" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$argv" fullname="$argv" type="array" children="1" numchildren="11" page="0" pagesize="100"><property name="0" fullname="$argv[0]" type="string" size="35" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3BocHVuaXQ=]]></property><property name="1" fullname="$argv[1]" type="string" size="11" encoding="base64"><![CDATA[LS1ib290c3RyYXA=]]></property><property name="2" fullname="$argv[2]" type="string" size="24" encoding="base64"><![CDATA[L2FwcC90ZXN0cy9ib290c3RyYXAucGhw]]></property><property name="3" fullname="$argv[3]" type="string" size="18" encoding="base64"><![CDATA[LS1uby1jb25maWd1cmF0aW9u]]></property><property name="4" fullname="$argv[4]" type="string" size="8" encoding="base64"><![CDATA[LS1maWx0ZXI=]]></property><property name="5" fullname="$argv[5]" type="string" size="159" encoding="base64"><![CDATA[Lyhjb21tb25cXGNoaXBcXGV4dGVybmFsSW50ZWdyYXRpb25zXFxrZXNzM1xcVGVzdHNcXEludGVncmF0aW9uXFxBcHBsaWNhdGlvblxcSGFuZGxlclxcU3RhcnREZWNvZGluZ0hhbmRsZXJUZXN0Ojp0ZXN0X2hhbmRsZV9zdWNjZXNzZnVsX2RlY29kaW5nX3N0YXJ0KSggLiopPyQv]]></property><property name="6" fullname="$argv[6]" type="string" size="13" encoding="base64"><![CDATA[LS10ZXN0LXN1ZmZpeA==]]></property><property name="7" fullname="$argv[7]" type="string" size="28" encoding="base64"><![CDATA[U3RhcnREZWNvZGluZ0hhbmRsZXJUZXN0LnBocA==]]></property><property name="8" fullname="$argv[8]" type="string" size="81" encoding="base64"><![CDATA[L2FwcC9jb21tb24vY2hpcC9leHRlcm5hbEludGVncmF0aW9ucy9rZXNzMy9UZXN0cy9JbnRlZ3JhdGlvbi9BcHBsaWNhdGlvbi9IYW5kbGVy]]></property><property name="9" fullname="$argv[9]" type="string" size="10" encoding="base64"><![CDATA[LS10ZWFtY2l0eQ==]]></property><property name="10" fullname="$argv[10]" type="string" size="46" encoding="base64"><![CDATA[LS1jYWNoZS1yZXN1bHQtZmlsZT0vYXBwLy5waHB1bml0LnJlc3VsdC5jYWNoZQ==]]></property></property><property name="$argc" fullname="$argc" type="int"><![CDATA[11]]></property><property name="$_ENV" fullname="$_ENV" type="array" children="1" numchildren="52" page="0" pagesize="100"><property name="PATH" fullname="$_ENV[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_ENV[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[ODI3NDI4YWNjODhk]]></property><property name="ROBOT_EMAIL" fullname="$_ENV[&quot;ROBOT_EMAIL&quot;]" type="string" size="28" encoding="base64"><![CDATA[****************************************]]></property><property name="VAR_DUMPER_SERVER" fullname="$_ENV[&quot;VAR_DUMPER_SERVER&quot;]" type="string" size="16" encoding="base64"><![CDATA[YnVnZ3JlZ2F0b3I6OTkxMg==]]></property><property name="AUTOPACK_API_URL" fullname="$_ENV[&quot;AUTOPACK_API_URL&quot;]" type="string" size="27" encoding="base64"><![CDATA[aHR0cDovL2NoaXBfYXBwX3B5dGhvbjo1MDAx]]></property><property name="ALIENTECH_API_URL" fullname="$_ENV[&quot;ALIENTECH_API_URL&quot;]" type="string" size="32" encoding="base64"><![CDATA[aHR0cHM6Ly9lbmNvZGluZ2FwaS5hbGllbnRlY2gudG8=]]></property><property name="DB_NAME" fullname="$_ENV[&quot;DB_NAME&quot;]" type="string" size="10" encoding="base64"><![CDATA[Y2hpcHR1bmluZw==]]></property><property name="DB_USERNAME" fullname="$_ENV[&quot;DB_USERNAME&quot;]" type="string" size="4" encoding="base64"><![CDATA[Y2hpcA==]]></property><property name="ALIENTECH_SECRET_KEY" fullname="$_ENV[&quot;ALIENTECH_SECRET_KEY&quot;]" type="string" size="22" encoding="base64"><![CDATA[Pyw7TlRuRDhYRD41WDktYjI1WW5UZw==]]></property><property name="TEST_DB_USERNAME" fullname="$_ENV[&quot;TEST_DB_USERNAME&quot;]" type="string" size="4" encoding="base64"><![CDATA[cm9vdA==]]></property><property name="COMPOSE_CONVERT_WINDOWS_PATHS" fullname="$_ENV[&quot;COMPOSE_CONVERT_WINDOWS_PATHS&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="FRONTEND_COOKIE_VALIDATION_KEY" fullname="$_ENV[&quot;FRONTEND_COOKIE_VALIDATION_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="GLIDE_SIGN_KEY" fullname="$_ENV[&quot;GLIDE_SIGN_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="BACKEND_COOKIE_VALIDATION_KEY" fullname="$_ENV[&quot;BACKEND_COOKIE_VALIDATION_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="TEST_DB_DSN" fullname="$_ENV[&quot;TEST_DB_DSN&quot;]" type="string" size="53" encoding="base64"><![CDATA[bXlzcWw6aG9zdD0xMjcuMC4wLjE7cG9ydD0zMzA2O2RibmFtZT1jaGlwdHVuaW5nX3Rlc3Q=]]></property><property name="TERM" fullname="$_ENV[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_ENV[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="24" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvYXV0b2xvYWQucGhw]]></property><property name="STORAGE_BASE_URL" fullname="$_ENV[&quot;STORAGE_BASE_URL&quot;]" type="string" size="12" encoding="base64"><![CDATA[L3N0b3JhZ2Uvd2Vi]]></property><property name="DB_PASSWORD" fullname="$_ENV[&quot;DB_PASSWORD&quot;]" type="string" size="12" encoding="base64"><![CDATA[cm9vdHBhc3N3b3Jk]]></property><property name="YII_ENV" fullname="$_ENV[&quot;YII_ENV&quot;]" type="string" size="3" encoding="base64"><![CDATA[ZGV2]]></property><property name="DB_TABLE_PREFIX" fullname="$_ENV[&quot;DB_TABLE_PREFIX&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="GLIDE_MAX_IMAGE_SIZE" fullname="$_ENV[&quot;GLIDE_MAX_IMAGE_SIZE&quot;]" type="string" size="7" encoding="base64"><![CDATA[NDAwMDAwMA==]]></property><property name="GITHUB_CLIENT_ID" fullname="$_ENV[&quot;GITHUB_CLIENT_ID&quot;]" type="string" size="14" encoding="base64"><![CDATA[eW91ci1jbGllbnQtaWQ=]]></property><property name="YII_DEBUG" fullname="$_ENV[&quot;YII_DEBUG&quot;]" type="string" size="4" encoding="base64"><![CDATA[dHJ1ZQ==]]></property><property name="AUTOPACK_PASSWORD" fullname="$_ENV[&quot;AUTOPACK_PASSWORD&quot;]" type="string" size="23" encoding="base64"><![CDATA[bWR4VkxEYTBtOHVyOENuVUJnNG96Nm8=]]></property><property name="VAR_DUMPER_FORMAT" fullname="$_ENV[&quot;VAR_DUMPER_FORMAT&quot;]" type="string" size="6" encoding="base64"><![CDATA[c2VydmVy]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_ENV[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="DB_DSN" fullname="$_ENV[&quot;DB_DSN&quot;]" type="string" size="41" encoding="base64"><![CDATA[bXlzcWw6aG9zdD1kYjtwb3J0PTMzMDY7ZGJuYW1lPWNoaXB0dW5pbmc=]]></property><property name="LINK_ASSETS" fullname="$_ENV[&quot;LINK_ASSETS&quot;]" type="string" size="5" encoding="base64"><![CDATA[ZmFsc2U=]]></property><property name="DB_CHARSET" fullname="$_ENV[&quot;DB_CHARSET&quot;]" type="string" size="7" encoding="base64"><![CDATA[dXRmOG1iNA==]]></property><property name="AUTOPACK_USERNAME" fullname="$_ENV[&quot;AUTOPACK_USERNAME&quot;]" type="string" size="8" encoding="base64"><![CDATA[YXV0b3BhY2s=]]></property><property name="BACKEND_BASE_URL" fullname="$_ENV[&quot;BACKEND_BASE_URL&quot;]" type="string" size="8" encoding="base64"><![CDATA[L2N0YWRtaW4=]]></property><property name="SMTP_HOST" fullname="$_ENV[&quot;SMTP_HOST&quot;]" type="string" size="11" encoding="base64"><![CDATA[bWFpbGNhdGNoZXI=]]></property><property name="FRONTEND_BASE_URL" fullname="$_ENV[&quot;FRONTEND_BASE_URL&quot;]" type="string" size="1" encoding="base64"><![CDATA[Lw==]]></property><property name="APP_MAINTENANCE" fullname="$_ENV[&quot;APP_MAINTENANCE&quot;]" type="string" size="1" encoding="base64"><![CDATA[MA==]]></property><property name="TEST_DB_PASSWORD" fullname="$_ENV[&quot;TEST_DB_PASSWORD&quot;]" type="string" size="18" encoding="base64"><![CDATA[UDI3SElMcXJnVDYyYVNvRFEm]]></property><property name="SMTP_PORT" fullname="$_ENV[&quot;SMTP_PORT&quot;]" type="string" size="4" encoding="base64"><![CDATA[MTAyNQ==]]></property><property name="ALIENTECH_CLIENT_GUID" fullname="$_ENV[&quot;ALIENTECH_CLIENT_GUID&quot;]" type="string" size="36" encoding="base64"><![CDATA[YzdmNDMzMDQtNjI0Yi00MmI5LTk5ZjgtOWQzNmM5ZjIzNGQz]]></property><property name="GITHUB_CLIENT_SECRET" fullname="$_ENV[&quot;GITHUB_CLIENT_SECRET&quot;]" type="string" size="18" encoding="base64"><![CDATA[eW91ci1jbGllbnQtc2VjcmV0]]></property><property name="MYSQL_ROOT_PASSWORD" fullname="$_ENV[&quot;MYSQL_ROOT_PASSWORD&quot;]" type="string" size="12" encoding="base64"><![CDATA[cm9vdHBhc3N3b3Jk]]></property><property name="ADMIN_EMAIL" fullname="$_ENV[&quot;ADMIN_EMAIL&quot;]" type="string" size="28" encoding="base64"><![CDATA[****************************************]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9FTlZbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_ENV[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_ENV[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_ENV[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_ENV[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_ENV[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_ENV[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI4]]></property><property name="PHP_URL" fullname="$_ENV[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjgudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_ENV[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjgudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_ENV[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[YWY4YzkxNTMxNTNhN2Y0ODkxNTNiN2E3NGYyZjI5YTVlZTM2ZjVjYjJjNmM2OTI5Yzk4NDExYTU3N2U4OWM5MQ==]]></property><property name="HOME" fullname="$_ENV[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property></property><property name="$_REQUEST" fullname="$_REQUEST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_SERVER" fullname="$_SERVER" type="array" children="1" numchildren="61" page="0" pagesize="100"><property name="PATH" fullname="$_SERVER[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_SERVER[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[ODI3NDI4YWNjODhk]]></property><property name="ROBOT_EMAIL" fullname="$_SERVER[&quot;ROBOT_EMAIL&quot;]" type="string" size="28" encoding="base64"><![CDATA[****************************************]]></property><property name="VAR_DUMPER_SERVER" fullname="$_SERVER[&quot;VAR_DUMPER_SERVER&quot;]" type="string" size="16" encoding="base64"><![CDATA[YnVnZ3JlZ2F0b3I6OTkxMg==]]></property><property name="AUTOPACK_API_URL" fullname="$_SERVER[&quot;AUTOPACK_API_URL&quot;]" type="string" size="27" encoding="base64"><![CDATA[aHR0cDovL2NoaXBfYXBwX3B5dGhvbjo1MDAx]]></property><property name="ALIENTECH_API_URL" fullname="$_SERVER[&quot;ALIENTECH_API_URL&quot;]" type="string" size="32" encoding="base64"><![CDATA[aHR0cHM6Ly9lbmNvZGluZ2FwaS5hbGllbnRlY2gudG8=]]></property><property name="DB_NAME" fullname="$_SERVER[&quot;DB_NAME&quot;]" type="string" size="10" encoding="base64"><![CDATA[Y2hpcHR1bmluZw==]]></property><property name="DB_USERNAME" fullname="$_SERVER[&quot;DB_USERNAME&quot;]" type="string" size="4" encoding="base64"><![CDATA[Y2hpcA==]]></property><property name="ALIENTECH_SECRET_KEY" fullname="$_SERVER[&quot;ALIENTECH_SECRET_KEY&quot;]" type="string" size="22" encoding="base64"><![CDATA[Pyw7TlRuRDhYRD41WDktYjI1WW5UZw==]]></property><property name="TEST_DB_USERNAME" fullname="$_SERVER[&quot;TEST_DB_USERNAME&quot;]" type="string" size="4" encoding="base64"><![CDATA[cm9vdA==]]></property><property name="COMPOSE_CONVERT_WINDOWS_PATHS" fullname="$_SERVER[&quot;COMPOSE_CONVERT_WINDOWS_PATHS&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="FRONTEND_COOKIE_VALIDATION_KEY" fullname="$_SERVER[&quot;FRONTEND_COOKIE_VALIDATION_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="GLIDE_SIGN_KEY" fullname="$_SERVER[&quot;GLIDE_SIGN_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="BACKEND_COOKIE_VALIDATION_KEY" fullname="$_SERVER[&quot;BACKEND_COOKIE_VALIDATION_KEY&quot;]" type="string" size="15" encoding="base64"><![CDATA[PGdlbmVyYXRlZF9rZXk+]]></property><property name="TEST_DB_DSN" fullname="$_SERVER[&quot;TEST_DB_DSN&quot;]" type="string" size="53" encoding="base64"><![CDATA[bXlzcWw6aG9zdD0xMjcuMC4wLjE7cG9ydD0zMzA2O2RibmFtZT1jaGlwdHVuaW5nX3Rlc3Q=]]></property><property name="TERM" fullname="$_SERVER[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_SERVER[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="24" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvYXV0b2xvYWQucGhw]]></property><property name="STORAGE_BASE_URL" fullname="$_SERVER[&quot;STORAGE_BASE_URL&quot;]" type="string" size="12" encoding="base64"><![CDATA[L3N0b3JhZ2Uvd2Vi]]></property><property name="DB_PASSWORD" fullname="$_SERVER[&quot;DB_PASSWORD&quot;]" type="string" size="12" encoding="base64"><![CDATA[cm9vdHBhc3N3b3Jk]]></property><property name="YII_ENV" fullname="$_SERVER[&quot;YII_ENV&quot;]" type="string" size="3" encoding="base64"><![CDATA[ZGV2]]></property><property name="DB_TABLE_PREFIX" fullname="$_SERVER[&quot;DB_TABLE_PREFIX&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="GLIDE_MAX_IMAGE_SIZE" fullname="$_SERVER[&quot;GLIDE_MAX_IMAGE_SIZE&quot;]" type="string" size="7" encoding="base64"><![CDATA[NDAwMDAwMA==]]></property><property name="GITHUB_CLIENT_ID" fullname="$_SERVER[&quot;GITHUB_CLIENT_ID&quot;]" type="string" size="14" encoding="base64"><![CDATA[eW91ci1jbGllbnQtaWQ=]]></property><property name="YII_DEBUG" fullname="$_SERVER[&quot;YII_DEBUG&quot;]" type="string" size="4" encoding="base64"><![CDATA[dHJ1ZQ==]]></property><property name="AUTOPACK_PASSWORD" fullname="$_SERVER[&quot;AUTOPACK_PASSWORD&quot;]" type="string" size="23" encoding="base64"><![CDATA[bWR4VkxEYTBtOHVyOENuVUJnNG96Nm8=]]></property><property name="VAR_DUMPER_FORMAT" fullname="$_SERVER[&quot;VAR_DUMPER_FORMAT&quot;]" type="string" size="6" encoding="base64"><![CDATA[c2VydmVy]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_SERVER[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="DB_DSN" fullname="$_SERVER[&quot;DB_DSN&quot;]" type="string" size="41" encoding="base64"><![CDATA[bXlzcWw6aG9zdD1kYjtwb3J0PTMzMDY7ZGJuYW1lPWNoaXB0dW5pbmc=]]></property><property name="LINK_ASSETS" fullname="$_SERVER[&quot;LINK_ASSETS&quot;]" type="string" size="5" encoding="base64"><![CDATA[ZmFsc2U=]]></property><property name="DB_CHARSET" fullname="$_SERVER[&quot;DB_CHARSET&quot;]" type="string" size="7" encoding="base64"><![CDATA[dXRmOG1iNA==]]></property><property name="AUTOPACK_USERNAME" fullname="$_SERVER[&quot;AUTOPACK_USERNAME&quot;]" type="string" size="8" encoding="base64"><![CDATA[YXV0b3BhY2s=]]></property><property name="BACKEND_BASE_URL" fullname="$_SERVER[&quot;BACKEND_BASE_URL&quot;]" type="string" size="8" encoding="base64"><![CDATA[L2N0YWRtaW4=]]></property><property name="SMTP_HOST" fullname="$_SERVER[&quot;SMTP_HOST&quot;]" type="string" size="11" encoding="base64"><![CDATA[bWFpbGNhdGNoZXI=]]></property><property name="FRONTEND_BASE_URL" fullname="$_SERVER[&quot;FRONTEND_BASE_URL&quot;]" type="string" size="1" encoding="base64"><![CDATA[Lw==]]></property><property name="APP_MAINTENANCE" fullname="$_SERVER[&quot;APP_MAINTENANCE&quot;]" type="string" size="1" encoding="base64"><![CDATA[MA==]]></property><property name="TEST_DB_PASSWORD" fullname="$_SERVER[&quot;TEST_DB_PASSWORD&quot;]" type="string" size="18" encoding="base64"><![CDATA[UDI3SElMcXJnVDYyYVNvRFEm]]></property><property name="SMTP_PORT" fullname="$_SERVER[&quot;SMTP_PORT&quot;]" type="string" size="4" encoding="base64"><![CDATA[MTAyNQ==]]></property><property name="ALIENTECH_CLIENT_GUID" fullname="$_SERVER[&quot;ALIENTECH_CLIENT_GUID&quot;]" type="string" size="36" encoding="base64"><![CDATA[YzdmNDMzMDQtNjI0Yi00MmI5LTk5ZjgtOWQzNmM5ZjIzNGQz]]></property><property name="GITHUB_CLIENT_SECRET" fullname="$_SERVER[&quot;GITHUB_CLIENT_SECRET&quot;]" type="string" size="18" encoding="base64"><![CDATA[eW91ci1jbGllbnQtc2VjcmV0]]></property><property name="MYSQL_ROOT_PASSWORD" fullname="$_SERVER[&quot;MYSQL_ROOT_PASSWORD&quot;]" type="string" size="12" encoding="base64"><![CDATA[cm9vdHBhc3N3b3Jk]]></property><property name="ADMIN_EMAIL" fullname="$_SERVER[&quot;ADMIN_EMAIL&quot;]" type="string" size="28" encoding="base64"><![CDATA[****************************************]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9TRVJWRVJbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_SERVER[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_SERVER[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_SERVER[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_SERVER[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_SERVER[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_SERVER[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI4]]></property><property name="PHP_URL" fullname="$_SERVER[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjgudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_SERVER[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjgudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_SERVER[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[YWY4YzkxNTMxNTNhN2Y0ODkxNTNiN2E3NGYyZjI5YTVlZTM2ZjVjYjJjNmM2OTI5Yzk4NDExYTU3N2U4OWM5MQ==]]></property><property name="HOME" fullname="$_SERVER[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property><property name="PHP_SELF" fullname="$_SERVER[&quot;PHP_SELF&quot;]" type="string" size="35" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3BocHVuaXQ=]]></property><property name="SCRIPT_NAME" fullname="$_SERVER[&quot;SCRIPT_NAME&quot;]" type="string" size="35" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3BocHVuaXQ=]]></property><property name="SCRIPT_FILENAME" fullname="$_SERVER[&quot;SCRIPT_FILENAME&quot;]" type="string" size="35" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3BocHVuaXQ=]]></property><property name="PATH_TRANSLATED" fullname="$_SERVER[&quot;PATH_TRANSLATED&quot;]" type="string" size="35" encoding="base64"><![CDATA[L2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3BocHVuaXQ=]]></property><property name="DOCUMENT_ROOT" fullname="$_SERVER[&quot;DOCUMENT_ROOT&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="REQUEST_TIME_FLOAT" fullname="$_SERVER[&quot;REQUEST_TIME_FLOAT&quot;]" type="float"><![CDATA[1748458204.2496]]></property><property name="REQUEST_TIME" fullname="$_SERVER[&quot;REQUEST_TIME&quot;]" type="int"><![CDATA[1748458204]]></property><property name="argv" fullname="$_SERVER[&quot;argv&quot;]" type="array" children="1" numchildren="11"></property><property name="argc" fullname="$_SERVER[&quot;argc&quot;]" type="int"><![CDATA[11]]></property></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property><property name="$file" fullname="$file" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 43 -d 0 -c 2
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="43" context="2"></response>

[1] [Step Debug] <- run -i 44
[1] [Step Debug] -> <notify xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" name="error"><xdebug:message filename="file:///app/console/config/base.php" lineno="214" type="Warning"><![CDATA[require(../../common/config/container_v2.php): Failed to open stream: No such file or directory]]></xdebug:message></notify>

[1] [Step Debug] -> <stream xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" type="stdout" encoding="base64"><![CDATA[Cldhcm5pbmc6IHJlcXVpcmUoLi4vLi4vY29tbW9uL2NvbmZpZy9jb250YWluZXJfdjIucGhwKTogRmFpbGVkIHRvIG9wZW4gc3RyZWFtOiBObyBzdWNoIGZpbGUgb3IgZGlyZWN0b3J5IGluIC9hcHAvY29uc29sZS9jb25maWcvYmFzZS5waHAgb24gbGluZSAyMTQK]]></stream>

[1] [Step Debug] -> <stream xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" type="stdout" encoding="base64"><![CDATA[UEhQVW5pdCAxMC40LjIgYnkgU2ViYXN0aWFuIEJlcmdtYW5uIGFuZCBjb250cmlidXRvcnMuCgpFcnJvciBpbiBib290c3RyYXAgc2NyaXB0OiBFcnJvcjoKRmFpbGVkIG9wZW5pbmcgcmVxdWlyZWQgJy4uLy4uL2NvbW1vbi9jb25maWcvY29udGFpbmVyX3YyLnBocCcgKGluY2x1ZGVfcGF0aD0nLjovdXNyL2xvY2FsL2xpYi9waHAnKQojMCAvYXBwL3Rlc3RzL2Jvb3RzdHJhcC5waHAoMjQpOiByZXF1aXJlKCkKIzEgL2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3NyYy9UZXh0VUkvQXBwbGljYXRpb24ucGhwKDI4Myk6IGluY2x1ZGVfb25jZSgnL2FwcC90ZXN0cy9ib290Li4uJykKIzIgL2FwcC92ZW5kb3IvcGhwdW5pdC9waHB1bml0L3NyYy9UZXh0VUkvQXBwbGljYXRpb24ucGhwKDEwMyk6IFBIUFVuaXRcVGV4dFVJXEFwcGxpY2F0aW9uLT5sb2FkQm9vdHN0cmFwU2NyaXB0KCcvYXBwL3Rlc3RzL2Jvb3QuLi4nKQojMyAvYXBwL3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdCg5OSk6IFBIUFVuaXRcVGV4dFVJXEFwcGxpY2F0aW9uLT5ydW4oQXJyYXkpCiM0IHttYWlufQo=]]></stream>

[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="run" transaction_id="44" status="stopping" reason="ok"></response>

[1] [Step Debug] <- eval -i 45 -- JEdMT0JBTFNbJ0lERV9FVkFMX0NBQ0hFJ11bJzQ4ZDNhOTcxLTFmZGItNGVjMS1hYzE0LTZhNGYyZDdkNmI0ZCddPWVycm9yX3JlcG9ydGluZygpOw==
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="45"><error code="5"><message><![CDATA[command is not available]]></message></error></response>

[1] Log closed at 2025-05-28 18:50:08.434236

