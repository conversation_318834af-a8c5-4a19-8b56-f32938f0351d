<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\ValueObject;

use InvalidArgumentException;

/**
 * Value Object для идентификатора проекта
 */
final readonly class ProjectId
{
    public function __construct(
        private int $value
    ) {
        $this->validate();
    }

    public static function fromInt(int $value): self
    {
        return new self($value);
    }

    public function getValue(): int
    {
        return $this->value;
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }

    private function validate(): void
    {
        if ($this->value <= 0) {
            throw new InvalidArgumentException('Project ID must be greater than 0');
        }
    }
}
