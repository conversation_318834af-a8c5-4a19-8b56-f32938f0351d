<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Entities;

use common\chip\externalIntegrations\kess3\Domain\ValueObjects\OperationStatus;
use common\chip\externalIntegrations\kess3\Domain\ValueObjects\UserInfo;
use common\chip\externalIntegrations\kess3\Interfaces\DecodingOperationInterface;

/**
 * Доменная сущность операции декодинга
 */
class DecodingOperation32432423 implements DecodingOperationInterface
{
    public function __construct(
        private readonly string $id,
        private OperationStatus $status,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly UserInfo $userInfo,
        private readonly \DateTimeImmutable $startedAt,
        private ?\DateTimeImmutable $completedAt = null,
        private ?string $error = null,
        private ?array $result = null,
        private ?int $recommendedPollingInterval = null
    ) {}
    
    public static function create(
        string $id,
        int $projectId,
        int $fileId,
        UserInfo $userInfo
    ): self {
        return new self(
            id: $id,
            status: OperationStatus::inProgress(),
            projectId: $projectId,
            fileId: $fileId,
            userInfo: $userInfo,
            startedAt: new \DateTimeImmutable()
        );
    }
    
    public function complete(bool $successful, ?array $result = null, ?string $error = null): void
    {
        $this->status = $successful ? OperationStatus::completed() : OperationStatus::failed();
        $this->completedAt = new \DateTimeImmutable();
        $this->result = $result;
        $this->error = $error;
    }
    
    public function updatePollingInterval(int $interval): void
    {
        $this->recommendedPollingInterval = $interval;
    }
    
    // Реализация DecodingOperationInterface
    public function getId(): string
    {
        return $this->id;
    }
    
    public function getStatus(): string
    {
        return $this->status->getValue();
    }
    
    public function isCompleted(): bool
    {
        return $this->status->isCompleted();
    }
    
    public function isSuccessful(): bool
    {
        return $this->status->isSuccessful();
    }
    
    public function hasFailed(): bool
    {
        return $this->status->isFailed();
    }
    
    public function getError(): ?string
    {
        return $this->error;
    }
    
    public function getResult(): ?array
    {
        return $this->result;
    }
    
    public function getUserInfo(): ?array
    {
        return $this->userInfo->toArray();
    }
    
    public function getStartedAt(): ?\DateTimeInterface
    {
        return $this->startedAt;
    }
    
    public function getCompletedAt(): ?\DateTimeInterface
    {
        return $this->completedAt;
    }
    
    // Доменные методы
    public function getProjectId(): int
    {
        return $this->projectId;
    }
    
    public function getFileId(): int
    {
        return $this->fileId;
    }
    
    public function getRecommendedPollingInterval(): ?int
    {
        return $this->recommendedPollingInterval;
    }
    
    public function getDuration(): ?int
    {
        if (!$this->completedAt) {
            return null;
        }
        
        return $this->completedAt->getTimestamp() - $this->startedAt->getTimestamp();
    }
}
