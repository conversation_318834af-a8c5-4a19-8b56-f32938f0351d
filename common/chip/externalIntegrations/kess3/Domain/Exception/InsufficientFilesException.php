<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение при недостатке модифицированных файлов для энкодирования
 */
final class InsufficientFilesException extends EncodingException
{
    public function __construct(
        int $projectId,
        int $foundFiles,
        int $requiredFiles = 1,
        ?\Throwable $previous = null
    ) {
        $message = "Project {$projectId} has insufficient files for encoding: found {$foundFiles}, required at least {$requiredFiles}";
        parent::__construct($message, 0, $previous);
    }

    public static function noModifiedFiles(int $projectId): self
    {
        return new self($projectId, 0, 1);
    }

    public static function missingRequiredFiles(int $projectId, array $missingTypes): self
    {
        $message = "Project {$projectId} is missing required file types for encoding: " . implode(', ', $missingTypes);
        return new EncodingException($message);
    }
}
