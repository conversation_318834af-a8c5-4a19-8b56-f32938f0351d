<?php

namespace common\chip\event\handlers;

use common\chip\event\core\EventInterface;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\event\EventDispatcher;
use common\chip\event\EventHandler;
use common\chip\event\EventSystemBootstrap;
use common\chip\event\project\FileEventFactory;
use common\chip\event\project\ProjectEventFactory;
use common\chip\project\entities\dto\ProjectNoteDto;
use common\chip\project\entities\dto\ProjectReportDto;
use common\chip\project\factories\ProjectFileFactory;
use common\chip\project\interfaces\CreateProjectFormInterface;
use common\chip\project\repositories\ProjectRepository;
use common\chip\project\services\MessageService;
use common\helpers\MessageHelper;
use common\helpers\ProjectHelper;
use common\models\ChipAddition;
use common\models\ProjectOptions;
use common\models\Projects;
use Yii;

/**
 * Обработчик для генерации цепочки событий при создании проекта
 */
class ProjectCreationHandler implements EventHandler
{
    private EventDispatcher $eventDispatcher;
    private ProjectEventFactory $eventFactory;

    /**
     * @param EventDispatcher|null $eventDispatcher Диспетчер событий
     * @param ProjectEventFactory|null $eventFactory Фабрика событий
     */
    public function __construct(
        ?EventDispatcher $eventDispatcher = null,
        ?ProjectEventFactory $eventFactory = null
    ) {
        $this->eventDispatcher = $eventDispatcher ?? EventSystemBootstrap::getDispatcher();
        $this->eventFactory = $eventFactory ?? new ProjectEventFactory();
    }
    
    /**
     * {@inheritdoc}
     */
    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof ProjectCreatedEvent;
    }
    
    /**
     * {@inheritdoc}
     */
    public function handle(EventInterface $event): void
    {
        if (!$this->canHandle($event)) {
            return;
        }
        $modelForm = $event->getContextValue('modelForm');

        $project = $this->doCreateProject($modelForm);
        if (!$project) {
            return;
        }

        $event->setContextData('project_id', $project->id);

        $this->doProjectOptions($modelForm, $project);
//        $this->doProjectMessages($project);
        $this->doProjectFiles($modelForm, $project);
    }

    private function doCreateProject(CreateProjectFormInterface $modelForm): ?Projects
    {
        $repository = new ProjectRepository();
        return $repository->createModelByForm($modelForm);
    }

    private function doProjectOptions(CreateProjectFormInterface $modelForm, Projects $project): void
    {
        if (empty($modelForm->additions)) {
            return;
        }

        $project->unlinkAll('projectOptions');

        foreach ($modelForm->additions as $key => $addition) {

            if (!isset($addition['addition_id'])) {
                continue;
            }

            $projectOption = new ProjectOptions();
            $projectOption->setAttributes([
                'project_id' => $project->id,
                'addition_id' => $addition['addition_id'],
                'ecu_addition_id' => $addition['ecu_addition_id'],
                'content' => $addition['comment'],
                'created_by' => Yii::$app->user->identity->id,
            ]);

            $project->link('projectOptions', $projectOption);
        }
    }

    /**
     * @param Projects|null $project
     * @return void
     */
    private function doProjectMessages(?Projects $project): void
    {
        $creator = $project->creator;

        $title = 'New project from ' . $creator->username;

        $messageParts = [
            $title,
            $project->brand->title,
            $project->model->title,
            $project->generation->title,
            $project->engine->title,
            'file required:' . (!empty($project->stage_id) ? $project->stage->title : ' no tuning required')
        ];

        $optionsRequest = !empty($project->options_request) ? json_decode($project->options_request, true) : [];
        $additionsCatalog = ChipAddition::find()->select('id,title')->indexBy('id')->asArray()->all();
        $additions = $optionsRequest['additions'] ?? [];

        $request = $additions['request'] ?? [];

        $messageService = new MessageService();
        $messageService->setProject($project);

        if (!empty($request)) {
            $requestCommentParts = [];

            if (!empty($optionsRequest['requestAdditions'])) {
                foreach ($optionsRequest['requestAdditions'] as $requestAddition) {
                    $requestCommentParts[] = trim($additionsCatalog[$requestAddition['addition_id']]['title']);
                }
            }

            if (!empty($request['dtc'])) {
                $requestCommentParts[] = "Request DTC:" . $request['dtc'];
            }

            $requestComment = implode(', ', $requestCommentParts);

            $titleOptionsRequest = 'Options request: ';

            $messageParts[] = $titleOptionsRequest . $requestComment;

        }

        foreach ($additions as $key => $addition) {
            if ($key == 'request' || !isset($addition['addition_id'])) {
                continue;
            }

            $additionTitle = $additionsCatalog[$addition['addition_id']]['title'];

            $comment = $additionTitle . ':' . (empty($addition['comment']) ? ' DTC: No' : ' DTC: ' . $addition['comment']);

            $messageParts[] = $comment;
        }

        $projectCreationMessage = implode(', ', $messageParts) . '. ';

        $report = new ProjectReportDto($project->id, $title, $projectCreationMessage);

        $messageService->addReportFromAuthor($report);

        $messageService->addReloadFromAuthor();

        $eventOptionsRequest = $this->eventFactory->createOptionsRequestEvent($project);
        $this->eventDispatcher->registerHandler(new OptionsRequestedHandler());
        $this->eventDispatcher->dispatch($eventOptionsRequest, 'options.requested', $project->id, $project->creator->id);
    }

    private function doProjectFiles(mixed $modelForm, ?Projects $project): void
    {
        if (empty($modelForm->file)) {
            return;
        }
        foreach ($modelForm->file as $file) {
            if (empty($file)) {
                continue;
            }

            $fileDto = ProjectFileFactory::createProjectFileDto($project->readMethod->master, [
                'project_id' => $project->id,
                'hash' => $file,
                'type' => 'init',
                'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
                'can_download' => 1,
            ]);

            $file = ProjectFileFactory::createProjectFile($fileDto);

            $fileEventFactory = new FileEventFactory();
            $fileEvent = $fileEventFactory->createFileUploadedEvent($file);
            $this->eventDispatcher->registerHandler(Yii::$container->get(FileEventHandler::class));
            $this->eventDispatcher->dispatch($fileEvent, 'file.uploaded');

        }
    }
}
