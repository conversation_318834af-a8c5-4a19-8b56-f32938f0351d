<?php

namespace common\chip\event;

use common\chip\event\handlers\AlientechFileHandler;
use common\chip\event\handlers\AutopackFileHandler;
use common\chip\event\handlers\FileEventHandler;
use common\chip\event\handlers\NotifyClientHandler;
use common\chip\event\handlers\ProjectCreationCascadeHandler;
use common\chip\event\handlers\ProjectSetupHandler;
use common\chip\event\project\ProjectEventFactory;
use common\chip\event\repositories\EventRepository;
use common\chip\event\routing\NotificationRouter;
use common\chip\externalIntegrations\alientech\Application\Decoding\EventHandler\DecodingEventHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\EventHandler\EncodingEventHandler;
use common\chip\notification\handlers\UniversalNotificationHandler;
use common\chip\project\services\MessageService;
use Yii;

/**
 * Начальная настройка обработчиков событий
 */
class EventHandlersSetup
{
    /**
     * Регистрирует стандартные обработчики событий
     *
     * @return void
     */
    public static function registerHandlers(): void
    {
        // Создаем репозиторий событий
        $eventRepository = new EventRepository();

        // Создаем диспетчер событий с репозиторием
        $dispatcher = new EventDispatcher($eventRepository);
        EventSystemBootstrap::setDispatcher($dispatcher);

        // 2. Регистрируем обработчик цепочки событий при создании проекта
        EventSystemBootstrap::registerHandler(
            new ProjectCreationCascadeHandler(
                null,
                new ProjectEventFactory()
            )
        );

        // Регистрируем универсальный обработчик уведомлений
        EventSystemBootstrap::registerHandler(
            new UniversalNotificationHandler(
                Yii::$container->get(NotificationRouter::class)
            )
        );

        // Регистрируем файловые обработчики с обновленными зависимостями
        EventSystemBootstrap::registerHandler(
            new AlientechFileHandler(
                Yii::$container->get(EventDispatcher::class)
            )
        );

        EventSystemBootstrap::registerHandler(
            new AutopackFileHandler(
                Yii::$container->get(EventDispatcher::class)
            )
        );

        EventSystemBootstrap::registerHandler(
            new FileEventHandler(
                Yii::$container->get(EventDispatcher::class)
            )
        );

        // 3. Регистрируем обработчик настройки проекта
        EventSystemBootstrap::registerHandler(
            new ProjectSetupHandler()
        );

        // 4. Регистрируем обработчик уведомления клиента
        EventSystemBootstrap::registerHandler(
            new NotifyClientHandler(new MessageService())
        );

        EventSystemBootstrap::registerHandler(
            Yii::$container->get(DecodingEventHandler::class)
        );

        EventSystemBootstrap::registerHandler(
            Yii::$container->get(EncodingEventHandler::class)
        );



    }
}
