<?php
namespace common\chip\project\entities\dto;

use common\helpers\MessageHelper;

class ProjectMessageNoticeTopDto extends ProjectMessageDto
{

    public function __construct(array $data)
    {
        parent::__construct($data);
        $this->type = MessageHelper::TYPE_NOTE;
        $this->sys = MessageHelper::VALUE_YES;
        $this->sms = MessageHelper::VALUE_NOT;
        $this->mail = MessageHelper::VALUE_NOT;
        $this->telegram = MessageHelper::VALUE_NOT;
    }
}