<?php

namespace common\chip\notification\channel;


use common\models\notification\Notification;

/**
 * Интерфейс канала доставки уведомлений
 */
interface NotificationChannelInterface
{
    /**
     * Получить идентификатор канала
     *
     * @return string
     */
    public function getId(): string;
    
    /**
     * Получить название канала
     *
     * @return string
     */
    public function getName(): string;
    
    /**
     * Проверить, активен ли канал
     *
     * @return bool
     */
    public function isActive(): bool;
    
    /**
     * Адаптировать уведомление для канала
     *
     * @param Notification $notification Оригинальное уведомление
     * @param mixed $recipient Получатель
     * @param string $role Роль получателя
     * @return mixed Адаптированное уведомление
     */
    public function adaptNotification(Notification $notification, $recipient, string $role);
    
    /**
     * Отправить уведомление
     *
     * @param mixed $notification Адаптированное уведомление
     * @param mixed $recipient Получатель
     * @return bool Успешность отправки
     */
    public function send($notification, $recipient): bool;
    
    /**
     * Проверить, поддерживает ли канал приоритет
     *
     * @param string $priority Приоритет
     * @return bool
     */
    public function supportsPriority(string $priority): bool;
    
    /**
     * Проверить, поддерживает ли канал тип события
     *
     * @param string $eventType Тип события
     * @return bool
     */
    public function supportsEventType(string $eventType): bool;
}
