<?php
namespace common\chip\autopack\entities\dto;

class AutopackTestModRequestDto
{

    private int $config_id;
    /**
     * @var int|mixed
     */
    private int $search_file;
    /**
     * @var int|mixed
     */

    /**
     * @param int $ecu_id
     */
    public function __construct(array $data)
    {
        $this->config_id = $data['config_id'] ?? 0;
        $this->search_file = $data['search_file'] ?? 0;
    }

    /**
     * @return int
     */
    public function getConfigId(): int
    {
        return $this->config_id;
    }

    /**
     * @param int $config_id
     */
    public function setConfigId(int $config_id): void
    {
        $this->config_id = $config_id;
    }

    /**
     * @return int
     */
    public function getSearchFile(): int
    {
        return $this->search_file;
    }

    /**
     * @param int $search_file
     */
    public function setSearchFile(int $search_file): void
    {
        $this->search_file = $search_file;
    }

}