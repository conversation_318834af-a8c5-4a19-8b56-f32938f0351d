<?php
namespace common\chip\alientech\entities\dto;

class AlientechLinkDto
{
    protected $apiUrl;
    protected $clientApplicationGUID;
    protected $secretKey;

    /**
     * AuthRepository constructor.
     * @param $apiUrl
     * @param $clientApplicationGUID
     * @param $secretKey
     */
    public function __construct($apiUrl, $clientApplicationGUID, $secretKey)
    {
        $this->apiUrl = $apiUrl;
        $this->clientApplicationGUID = $clientApplicationGUID;
        $this->secretKey = $secretKey;
    }

    public static function fromEnv(): self
    {
        return new AlientechLinkDto(env('ALIENTECH_API_URL'), env('ALIENTECH_CLIENT_GUID'), env('ALIENTECH_SECRET_KEY'));
    }

    /**
     * @return mixed
     */
    public function getApiUrl()
    {
        return $this->apiUrl;
    }

    /**
     * @return mixed
     */
    public function getClientApplicationGUID()
    {
        return $this->clientApplicationGUID;
    }

    /**
     * @return mixed
     */
    public function getSecretKey()
    {
        return $this->secretKey;
    }

    /**
     * @param mixed $apiUrl
     */
    public function setApiUrl($apiUrl): void
    {
        $this->apiUrl = $apiUrl;
    }

    /**
     * @param mixed $clientApplicationGUID
     */
    public function setClientApplicationGUID($clientApplicationGUID): void
    {
        $this->clientApplicationGUID = $clientApplicationGUID;
    }

    /**
     * @param mixed $secretKey
     */
    public function setSecretKey($secretKey): void
    {
        $this->secretKey = $secretKey;
    }


}