<?php
namespace common\chip\alientech\services;

class AlientechService
{


    /**
     * AlientechLinkService constructor.
     */
    public function __construct()
    {
    }

    /**
     * @return string
     */
    public function getCustomerCode(): string
    {
        return 'user037';
    }

    public function getDecodeCallbackUrl()
    {
        return \Yii::$app->urlManagerFrontend->createAbsoluteUrl('/api/kess3-decoded');
    }

    public function getEncodeCallbackUrl()
    {
        return \Yii::$app->urlManagerFrontend->createAbsoluteUrl('/api/kess3-encoded');
    }

}