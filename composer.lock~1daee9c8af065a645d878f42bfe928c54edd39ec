{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c76e6883af72725f493abc9aff6c1e04", "packages": [{"name": "alexantr/yii2-elfinder", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/alexantr/yii2-elfinder.git", "reference": "868088746cefe3fdc576475ae50a398eeba86e99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alexantr/yii2-elfinder/zipball/868088746cefe3fdc576475ae50a398eeba86e99", "reference": "868088746cefe3fdc576475ae50a398eeba86e99", "shasum": ""}, "require": {"studio-42/elfinder": "^2.1.49", "yiisoft/yii2-jui": "~2.0.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^7.5"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"alexantr\\elfinder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "elFinder file manager for Yii 2", "keywords": ["elfinder", "file manager", "widget", "yii2"], "time": "2019-05-14T13:11:53+00:00"}, {"name": "asofter/yii2-imperavi-redactor", "version": "dev-master", "target-dir": "yii/imperavi", "source": {"type": "git", "url": "https://github.com/asofter/yii2-imperavi-redactor.git", "reference": "24c9a1e83dd44f18953bef876099366d5907b95e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asofter/yii2-imperavi-redactor/zipball/24c9a1e83dd44f18953bef876099366d5907b95e", "reference": "24c9a1e83dd44f18953bef876099366d5907b95e", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-0": {"yii\\imperavi\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}], "description": "Imperavi Redactor WYSIWYG widget (OEM-licensed for Yii 2)", "keywords": ["imperavi", "imperavi redactor", "yii"], "time": "2017-12-16T06:41:54+00:00"}, {"name": "bower-asset/ace-builds", "version": "v1.4.4", "source": {"type": "git", "url": "https://github.com/ajaxorg/ace-builds.git", "reference": "5fe4368221f48b114387c2e7d67c9cd904ec30a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ajaxorg/ace-builds/zipball/5fe4368221f48b114387c2e7d67c9cd904ec30a2", "reference": "5fe4368221f48b114387c2e7d67c9cd904ec30a2"}, "type": "bower-asset", "license": ["BSD"]}, {"name": "bower-asset/blueimp-canvas-to-blob", "version": "v3.14.0", "source": {"type": "git", "url": "**************:blueimp/JavaScript-Canvas-to-Blob.git", "reference": "9f3deb2e710d39c72a988559609d9dc9a319de0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/blueimp/JavaScript-Canvas-to-Blob/zipball/9f3deb2e710d39c72a988559609d9dc9a319de0b", "reference": "9f3deb2e710d39c72a988559609d9dc9a319de0b"}, "type": "bower-asset"}, {"name": "bower-asset/blueimp-file-upload", "version": "v9.31.0", "source": {"type": "git", "url": "https://github.com/blueimp/jQuery-File-Upload.git", "reference": "b096b1a5544299b59f2e05cf764a123ce3e32dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/blueimp/jQuery-File-Upload/zipball/b096b1a5544299b59f2e05cf764a123ce3e32dd3", "reference": "b096b1a5544299b59f2e05cf764a123ce3e32dd3"}, "require": {"bower-asset/blueimp-canvas-to-blob": ">=2.1.1", "bower-asset/blueimp-load-image": ">=1.13.0", "bower-asset/blueimp-tmpl": ">=2.5.4", "bower-asset/jquery": ">=1.6"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/blueimp-load-image", "version": "v2.21.0", "source": {"type": "git", "url": "https://github.com/blueimp/JavaScript-Load-Image.git", "reference": "e9a4fc2f0e8019ded69178c5b74d090cc6d8051c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/blueimp/JavaScript-Load-Image/zipball/e9a4fc2f0e8019ded69178c5b74d090cc6d8051c", "reference": "e9a4fc2f0e8019ded69178c5b74d090cc6d8051c"}, "type": "bower-asset"}, {"name": "bower-asset/blueimp-tmpl", "version": "v3.11.0", "source": {"type": "git", "url": "**************:blueimp/JavaScript-Templates.git", "reference": "da7647cb93fff2030e73a701db22c66de76a9919"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/blueimp/JavaScript-Templates/zipball/da7647cb93fff2030e73a701db22c66de76a9919", "reference": "da7647cb93fff2030e73a701db22c66de76a9919"}, "type": "bower-asset"}, {"name": "bower-asset/bootstrap", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/68b0d231a13201eb14acd3dc84e51543d16e5f7e", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/eonasdan-bootstrap-datetimepicker", "version": "4.17.47", "source": {"type": "git", "url": "https://github.com/Eonasdan/bootstrap-datetimepicker.git", "reference": "25c11d79e614bc6463a87c3dd9cbf8280422e006"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Eonasdan/bootstrap-datetimepicker/zipball/25c11d79e614bc6463a87c3dd9cbf8280422e006", "reference": "25c11d79e614bc6463a87c3dd9cbf8280422e006"}, "require": {"bower-asset/jquery": ">=1.8.3", "bower-asset/moment": ">=2.10.5"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/inputmask", "version": "3.3.11", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/5e670ad62f50c738388d4dcec78d2888505ad77b", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "15bc73803f76bc53b654b9fdbbbc096f56d7c03d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/15bc73803f76bc53b654b9fdbbbc096f56d7c03d", "reference": "15bc73803f76bc53b654b9fdbbbc096f56d7c03d"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/jquery-ui", "version": "1.12.1", "source": {"type": "git", "url": "**************:components/jqueryui.git", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/jqueryui/zipball/44ecf3794cc56b65954cc19737234a3119d036cc", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "require": {"bower-asset/jquery": ">=1.6"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/moment", "version": "2.24.0", "source": {"type": "git", "url": "https://github.com/moment/moment.git", "reference": "96d0d6791ab495859d09a868803d31a55c917de1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moment/moment/zipball/96d0d6791ab495859d09a868803d31a55c917de1", "reference": "96d0d6791ab495859d09a868803d31a55c917de1"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v1.3.2", "source": {"type": "git", "url": "**************:bestiejs/punycode.js.git", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bestiejs/punycode.js/zipball/38c8d3131a82567bfef18da09f7f4db68c84f8a3", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "type": "bower-asset"}, {"name": "bower-asset/swagger-ui", "version": "v3.22.2", "source": {"type": "git", "url": "https://github.com/swagger-api/swagger-ui.git", "reference": "54c045fd472a740e7ae3d26148708455fa6358b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swagger-api/swagger-ui/zipball/54c045fd472a740e7ae3d26148708455fa6358b4", "reference": "54c045fd472a740e7ae3d26148708455fa6358b4"}, "type": "bower-asset"}, {"name": "bower-asset/yii2-pjax", "version": "*******", "source": {"type": "git", "url": "**************:yiisoft/jquery-pjax.git", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/aef7b953107264f00234902a3880eb50dafc48be", "reference": "aef7b953107264f00234902a3880eb50dafc48be"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2018-03-26T11:24:36+00:00"}, {"name": "doctrine/annotations", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/53120e0eb10355388d6ccbe462f1fea34ddadb24", "reference": "53120e0eb10355388d6ccbe462f1fea34ddadb24", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": "^7.1"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2019-03-25T19:12:02+00:00"}, {"name": "doctrine/lexer", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "http://www.doctrine-project.org", "keywords": ["lexer", "parser"], "time": "2014-09-09T13:34:57+00:00"}, {"name": "egulias/email-validator", "version": "2.1.8", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "c26463ff9241f27907112fbcd0c86fa670cfef98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/c26463ff9241f27907112fbcd0c86fa670cfef98", "reference": "c26463ff9241f27907112fbcd0c86fa670cfef98", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">= 5.5"}, "require-dev": {"dominicsayers/isemail": "dev-master", "phpunit/phpunit": "^4.8.35||^5.7||^6.0", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2019-05-16T22:02:54+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.10.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "d85d39da4576a6934b72480be6978fb10c860021"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/d85d39da4576a6934b72480be6978fb10c860021", "reference": "d85d39da4576a6934b72480be6978fb10c860021", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "^1.1"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2018-02-23T01:58:20+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "9f83dded91781a01c63574e387eaa769be769115"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/9f83dded91781a01c63574e387eaa769be769115", "reference": "9f83dded91781a01c63574e387eaa769be769115", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2018-12-04T20:46:45+00:00"}, {"name": "intervention/image", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "e82d274f786e3d4b866a59b173f42e716f0783eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/e82d274f786e3d4b866a59b173f42e716f0783eb", "reference": "e82d274f786e3d4b866a59b173f42e716f0783eb", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "time": "2018-05-29T14:19:03+00:00"}, {"name": "johnitvn/yii2-ajax<PERSON><PERSON>", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/johnitvn/yii2-ajaxcrud.git", "reference": "563c2d57a13fc307c2ad2f9e69427581568e26fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnitvn/yii2-ajaxcrud/zipball/563c2d57a13fc307c2ad2f9e69427581568e26fd", "reference": "563c2d57a13fc307c2ad2f9e69427581568e26fd", "shasum": ""}, "require": {"kartik-v/yii2-editable": "^1.7.3", "kartik-v/yii2-grid": "^3.0.4", "kartik-v/yii2-mpdf": "^1.0.0", "yiisoft/yii2": "*", "yiisoft/yii2-bootstrap": "*", "yiisoft/yii2-gii": "*"}, "type": "yii2-extension", "extra": {"bootstrap": "johnitvn\\ajaxcrud\\Bootstrap"}, "autoload": {"psr-4": {"johnitvn\\ajaxcrud\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/johnitvn?tab=repositories"}], "description": "Gii CRUD template for Single Page Ajax Administration for yii2", "keywords": ["ajax", "crud", "database", "extension", "gii", "template", "yii2"], "time": "2016-03-21T10:56:25+00:00"}, {"name": "kartik-v/bootstrap-fileinput", "version": "v4.5.3", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git", "reference": "64e083b0414d294d99329fa94194f24345fc334a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-fileinput/zipball/64e083b0414d294d99329fa94194f24345fc334a", "reference": "64e083b0414d294d99329fa94194f24345fc334a", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\fileinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced HTML 5 file input for Bootstrap 3.x with features for file preview for many file types, multiple selection, ajax uploads, and more.", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "keywords": ["ajax", "bootstrap", "delete", "file", "image", "input", "j<PERSON>y", "multiple", "preview", "progress", "upload"], "time": "2019-03-21T11:44:25+00:00"}, {"name": "kartik-v/bootstrap-popover-x", "version": "v1.4.7", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-popover-x.git", "reference": "d4fc84a0776250f47840eb1594950a1702d8424d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-popover-x/zipball/d4fc84a0776250f47840eb1594950a1702d8424d", "reference": "d4fc84a0776250f47840eb1594950a1702d8424d", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Bootstrap Popover Extended - Popover with modal behavior, styling enhancements and more.", "homepage": "https://github.com/kartik-v/bootstrap-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "time": "2018-09-14T05:15:26+00:00"}, {"name": "kartik-v/bootstrap-star-rating", "version": "v4.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-star-rating.git", "reference": "cb449271f24c78ab54726232daf5b9dae3e49dbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-star-rating/zipball/cb449271f24c78ab54726232daf5b9dae3e49dbd", "reference": "cb449271f24c78ab54726232daf5b9dae3e49dbd", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A simple yet powerful JQuery star rating plugin for Bootstrap.", "homepage": "https://github.com/kartik-v/bootstrap-star-rating", "keywords": ["Rating", "awesome", "bootstrap", "font", "glyphicon", "star", "svg"], "time": "2019-05-25T06:53:46+00:00"}, {"name": "kartik-v/dependent-dropdown", "version": "v1.4.9", "source": {"type": "git", "url": "https://github.com/kartik-v/dependent-dropdown.git", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/dependent-dropdown/zipball/54a8806002ee21b744508a2edb95ed01d35c6cf9", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A multi level dependent dropdown JQuery plugin that allows nested dependencies.", "homepage": "https://github.com/kartik-v/dependent-dropdown", "keywords": ["dependent", "dropdown", "j<PERSON>y", "option", "select"], "time": "2019-03-09T10:53:11+00:00"}, {"name": "kartik-v/yii2-bootstrap4-dropdown", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-bootstrap4-dropdown.git", "reference": "399150300e7b0a9b93d51f43e583c1354db65488"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-bootstrap4-dropdown/zipball/399150300e7b0a9b93d51f43e583c1354db65488", "reference": "399150300e7b0a9b93d51f43e583c1354db65488", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9", "yiisoft/yii2-bootstrap4": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\bs4dropdown\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Bootstrap 4 dropdown widget for Yii2 with nested submenu support", "homepage": "https://github.com/kartik-v/yii2-bootstrap4-dropdown", "keywords": ["bootstrap", "dropdown", "j<PERSON>y", "nested", "submenu", "yii2"], "time": "2019-05-25T07:11:53+00:00"}, {"name": "kartik-v/yii2-dialog", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dialog.git", "reference": "6afae03a4d9f7b99ddc173d2a29dd6e3cc1c56cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dialog/zipball/6afae03a4d9f7b99ddc173d2a29dd6e3cc1c56cb", "reference": "6afae03a4d9f7b99ddc173d2a29dd6e3cc1c56cb", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\dialog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An asset bundle for bootstrap3-dialog for Yii 2.0 framework.", "homepage": "https://github.com/kartik-v/yii2-dialog", "keywords": ["alert", "bootstrap", "dialog", "extension", "modal", "yii2"], "time": "2018-09-06T19:34:03+00:00"}, {"name": "kartik-v/yii2-dropdown-x", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dropdown-x.git", "reference": "4689a7635e290ef1b71eb4f0962e3b77e347b6fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dropdown-x/zipball/4689a7635e290ef1b71eb4f0962e3b77e347b6fe", "reference": "4689a7635e290ef1b71eb4f0962e3b77e347b6fe", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\dropdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap dropdown widget for Yii 2 with submenu drilldown.", "homepage": "https://github.com/kartik-v/yii2-dropdown-x", "keywords": ["Context", "click", "extension", "menu", "mouse", "widget", "yii2"], "time": "2014-12-08T04:31:32+00:00"}, {"name": "kartik-v/yii2-editable", "version": "v1.7.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-editable.git", "reference": "dd80cff630d583e980641420fcf7044ee2df33de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-editable/zipball/dd80cff630d583e980641420fcf7044ee2df33de", "reference": "dd80cff630d583e980641420fcf7044ee2df33de", "shasum": ""}, "require": {"kartik-v/yii2-popover-x": "~1.3", "kartik-v/yii2-widget-activeform": ">=1.5.7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"kartik\\editable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced editable widget for Yii 2.0 that allows easy editing of displayed data with numerous configuration possibilities.", "homepage": "https://github.com/kartik-v/yii2-editable", "keywords": ["bootstrap", "editable", "input", "j<PERSON>y", "popover", "popover-x", "widget"], "time": "2018-10-03T07:02:33+00:00"}, {"name": "kartik-v/yii2-grid", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-grid.git", "reference": "0f80a28661883c997441dd89aff5d2a59c51e221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-grid/zipball/0f80a28661883c997441dd89aff5d2a59c51e221", "reference": "0f80a28661883c997441dd89aff5d2a59c51e221", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0"}, "suggest": {"kartik-v/yii2-mpdf": "For exporting grids to PDF"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\grid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Yii 2 GridView on steroids. Various enhancements and utilities for the Yii 2.0 GridView widget.", "homepage": "https://github.com/kartik-v/yii2-grid", "keywords": ["extension", "grid", "widget", "yii2"], "time": "2019-05-17T06:55:03+00:00"}, {"name": "kartik-v/yii2-krajee-base", "version": "v1.9.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-krajee-base.git", "reference": "1693374160c24443524ddc23508d2eb2955443f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-krajee-base/zipball/1693374160c24443524ddc23508d2eb2955443f4", "reference": "1693374160c24443524ddc23508d2eb2955443f4", "shasum": ""}, "require": {"yiisoft/yii2-bootstrap": "@dev"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"kartik\\base\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Base library and foundation components for all Yii2 Krajee extensions.", "homepage": "https://github.com/kartik-v/yii2-krajee-base", "keywords": ["base", "extension", "foundation", "krajee", "widget", "yii2"], "time": "2018-09-27T18:02:35+00:00"}, {"name": "kartik-v/yii2-mpdf", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-mpdf.git", "reference": "908c8d15aec62cd0ae81a1ce8c1eadcc18c8f7fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-mpdf/zipball/908c8d15aec62cd0ae81a1ce8c1eadcc18c8f7fc", "reference": "908c8d15aec62cd0ae81a1ce8c1eadcc18c8f7fc", "shasum": ""}, "require": {"mpdf/mpdf": "~7.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\mpdf\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper component for the mPDF library which generates PDF files from UTF-8 encoded HTML.", "homepage": "https://github.com/kartik-v/yii2-mpdf", "keywords": ["component", "extension", "html", "mpdf", "pdf", "utf8", "yii2"], "time": "2018-10-13T07:38:33+00:00"}, {"name": "kartik-v/yii2-nav-x", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-nav-x.git", "reference": "9ab9f611325e170648f51720068efcfc0a28751c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-nav-x/zipball/9ab9f611325e170648f51720068efcfc0a28751c", "reference": "9ab9f611325e170648f51720068efcfc0a28751c", "shasum": ""}, "require": {"kartik-v/yii2-dropdown-x": "~1.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\nav\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap Nav widget for Yii 2 with submenu drilldown.", "homepage": "https://github.com/kartik-v/yii2-nav-x", "keywords": ["Context", "click", "extension", "menu", "mouse", "widget", "yii2"], "time": "2018-09-19T18:16:36+00:00"}, {"name": "kartik-v/yii2-popover-x", "version": "v1.3.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-popover-x.git", "reference": "58837d63e65caa41f0c13e671d1e2abeec17887d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-popover-x/zipball/58837d63e65caa41f0c13e671d1e2abeec17887d", "reference": "58837d63e65caa41f0c13e671d1e2abeec17887d", "shasum": ""}, "require": {"kartik-v/bootstrap-popover-x": "~1.4", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.0 popover widget which combines both the bootstrap popover and modal features and includes various new styling enhancements.", "homepage": "https://github.com/kartik-v/yii2-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "time": "2017-09-08T04:30:42+00:00"}, {"name": "kartik-v/yii2-widget-activeform", "version": "v1.5.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-activeform.git", "reference": "02baee5e06a5dfbc94b71dc8c13ca87789716b29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-activeform/zipball/02baee5e06a5dfbc94b71dc8c13ca87789716b29", "reference": "02baee5e06a5dfbc94b71dc8c13ca87789716b29", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9.8"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\form\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 active-form and active-field with full bootstrap styling support (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-activeform", "keywords": ["activefield", "activeform", "extension", "field", "form", "widget", "yii2"], "time": "2019-02-23T19:45:01+00:00"}, {"name": "kartik-v/yii2-widget-affix", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-affix.git", "reference": "2184119bfa518c285406156f744769b13b861712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-affix/zipball/2184119bfa518c285406156f744769b13b861712", "reference": "2184119bfa518c285406156f744769b13b861712", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\affix\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A scrollspy and affixed enhanced navigation to highlight page sections (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-affix", "keywords": ["affix", "bootstrap", "extension", "j<PERSON>y", "navigation", "plugin", "scrollspy", "widget", "yii2"], "time": "2014-11-09T04:56:27+00:00"}, {"name": "kartik-v/yii2-widget-alert", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-alert.git", "reference": "7348b0d047695a3e552888c481ce250cbc1f9d0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-alert/zipball/7348b0d047695a3e552888c481ce250cbc1f9d0d", "reference": "7348b0d047695a3e552888c481ce250cbc1f9d0d", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\alert\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate alert based notifications using bootstrap-alert plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-alert", "keywords": ["alert", "block", "bootstrap", "extension", "flash", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "time": "2017-03-10T17:08:52+00:00"}, {"name": "kartik-v/yii2-widget-colorinput", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-colorinput.git", "reference": "65f2c4b603ef0c26c3c85b0246a6a59f59045a43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-colorinput/zipball/65f2c4b603ef0c26c3c85b0246a6a59f59045a43", "reference": "65f2c4b603ef0c26c3c85b0246a6a59f59045a43", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\color\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 color input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-colorinput", "keywords": ["HTML5", "color", "extension", "form", "input", "j<PERSON>y", "plugin", "widget", "yii2"], "time": "2018-09-12T03:12:07+00:00"}, {"name": "kartik-v/yii2-widget-datepicker", "version": "v1.4.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datepicker.git", "reference": "01a5940fb1b70b39b7916a4e68768f8626024ddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datepicker/zipball/01a5940fb1b70b39b7916a4e68768f8626024ddc", "reference": "01a5940fb1b70b39b7916a4e68768f8626024ddc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\date\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datepicker plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-datepicker", "keywords": ["date", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "time": "2018-08-29T12:10:45+00:00"}, {"name": "kartik-v/yii2-widget-datetimepicker", "version": "v1.4.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datetimepicker.git", "reference": "22d8c40665d59b76383e658de6ef2c187ed0da14"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datetimepicker/zipball/22d8c40665d59b76383e658de6ef2c187ed0da14", "reference": "22d8c40665d59b76383e658de6ef2c187ed0da14", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\datetime\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datetimepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-datetimepicker", "keywords": ["datetime", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "time": "2018-08-29T11:42:11+00:00"}, {"name": "kartik-v/yii2-widget-depdrop", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-depdrop.git", "reference": "6918ca6f7d7be153c80f6aa9c261f9b333293e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-depdrop/zipball/6918ca6f7d7be153c80f6aa9c261f9b333293e9c", "reference": "6918ca6f7d7be153c80f6aa9c261f9b333293e9c", "shasum": ""}, "require": {"kartik-v/dependent-dropdown": "~1.4", "kartik-v/yii2-krajee-base": "~1.7"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Widget that enables setting up dependent dropdowns with nested dependencies (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-depdrop", "keywords": ["dependent", "dropdown", "extension", "form", "j<PERSON>y", "plugin", "widget", "yii2"], "time": "2016-01-10T17:30:48+00:00"}, {"name": "kartik-v/yii2-widget-fileinput", "version": "v1.0.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-fileinput.git", "reference": "7f76d784cc48733746ff90b53b8474dcf48ea6c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-fileinput/zipball/7f76d784cc48733746ff90b53b8474dcf48ea6c4", "reference": "7f76d784cc48733746ff90b53b8474dcf48ea6c4", "shasum": ""}, "require": {"kartik-v/bootstrap-fileinput": "~4.4", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\file\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced FileInput widget for Bootstrap 3.x & 4.x with file preview, multiple selection, and more features (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-fileinput", "keywords": ["extension", "file", "form", "input", "j<PERSON>y", "plugin", "upload", "widget", "yii2"], "time": "2018-09-19T13:09:42+00:00"}, {"name": "kartik-v/yii2-widget-growl", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-growl.git", "reference": "c79abaa47e9103e93345cd1eca7bc75e17e9a92e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-growl/zipball/c79abaa47e9103e93345cd1eca7bc75e17e9a92e", "reference": "c79abaa47e9103e93345cd1eca7bc75e17e9a92e", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\growl\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate growl based notifications using bootstrap-growl plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-growl", "keywords": ["alert", "bootstrap", "extension", "growl", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "time": "2015-05-03T08:23:04+00:00"}, {"name": "kartik-v/yii2-widget-rangeinput", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rangeinput.git", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rangeinput/zipball/dd9019bab7e5bf570a02870d9e74387891bbdb32", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\range\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 range input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rangeinput", "keywords": ["HTML5", "extension", "form", "input", "j<PERSON>y", "plugin", "range", "widget", "yii2"], "time": "2018-09-07T10:05:08+00:00"}, {"name": "kartik-v/yii2-widget-rating", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rating.git", "reference": "651eaa5e6a3bd19471e2a907fb17c3fd92f383e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rating/zipball/651eaa5e6a3bd19471e2a907fb17c3fd92f383e7", "reference": "651eaa5e6a3bd19471e2a907fb17c3fd92f383e7", "shasum": ""}, "require": {"kartik-v/bootstrap-star-rating": "~4.0", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\rating\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 widget for the simple yet powerful bootstrap-star-rating plugin with fractional rating support (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rating", "keywords": ["Rating", "bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "star", "widget", "yii2"], "time": "2018-09-16T09:30:44+00:00"}, {"name": "kartik-v/yii2-widget-select2", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-select2.git", "reference": "f1ae98b7ec3e46ba0193471647ce15d07aa67f58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-select2/zipball/f1ae98b7ec3e46ba0193471647ce15d07aa67f58", "reference": "f1ae98b7ec3e46ba0193471647ce15d07aa67f58", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\select2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Select2 jQuery plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-select2", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "select2", "widget", "yii2"], "time": "2018-09-07T08:11:16+00:00"}, {"name": "kartik-v/yii2-widget-sidenav", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-sidenav.git", "reference": "02ee4f142d7dfbb316f878e538cc7b946f4502d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-sidenav/zipball/02ee4f142d7dfbb316f878e538cc7b946f4502d2", "reference": "02ee4f142d7dfbb316f878e538cc7b946f4502d2", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\sidenav\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced side navigation menu styled for bootstrap (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-sidenav", "keywords": ["bootstrap", "extension", "j<PERSON>y", "menu", "navigation", "plugin", "sidenav", "widget", "yii2"], "time": "2014-11-09T08:07:23+00:00"}, {"name": "kartik-v/yii2-widget-spinner", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-spinner.git", "reference": "3132ba14d58e0564d17f3b846b04c42aa72bdde3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-spinner/zipball/3132ba14d58e0564d17f3b846b04c42aa72bdde3", "reference": "3132ba14d58e0564d17f3b846b04c42aa72bdde3", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\spinner\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to render animated CSS3 loading spinners with VML fallback for IE (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-spinner", "keywords": ["CSS3", "extension", "j<PERSON>y", "loading", "plugin", "spinner", "widget", "yii2"], "time": "2014-11-09T05:02:05+00:00"}, {"name": "kartik-v/yii2-widget-switchinput", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-switchinput.git", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-switchinput/zipball/7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\switchinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle switchinputes (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-switchinput", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "switchinput", "toggle", "widget", "yii2"], "time": "2016-01-10T16:47:35+00:00"}, {"name": "kartik-v/yii2-widget-timepicker", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-timepicker.git", "reference": "203aeb9123e7fa9a3c584d206793d82abe96d469"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-timepicker/zipball/203aeb9123e7fa9a3c584d206793d82abe96d469", "reference": "203aeb9123e7fa9a3c584d206793d82abe96d469", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\time\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap timepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-timepicker", "keywords": ["bootstrap", "extension", "form", "j<PERSON>y", "picker", "plugin", "time", "widget", "yii2"], "time": "2017-01-08T06:36:24+00:00"}, {"name": "kartik-v/yii2-widget-touchspin", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-touchspin.git", "reference": "1daca822ad11258242178155613da8e269e3df2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-touchspin/zipball/1daca822ad11258242178155613da8e269e3df2b", "reference": "1daca822ad11258242178155613da8e269e3df2b", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\touchspin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle touchspines (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-touchspin", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "spinner", "touch", "widget", "yii2"], "time": "2018-09-05T11:59:34+00:00"}, {"name": "kartik-v/yii2-widget-typeahead", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-typeahead.git", "reference": "8fcf3c122bdd63545f462a137ac9acacba60f8d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-typeahead/zipball/8fcf3c122bdd63545f462a137ac9acacba60f8d6", "reference": "8fcf3c122bdd63545f462a137ac9acacba60f8d6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "~1.8"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\typeahead\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Twitter Typeahead plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-typeahead", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "typeahead", "widget", "yii2"], "time": "2018-08-05T14:19:09+00:00"}, {"name": "kartik-v/yii2-widgets", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widgets.git", "reference": "8c108835456d529984e6acd173455954958b1b18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widgets/zipball/8c108835456d529984e6acd173455954958b1b18", "reference": "8c108835456d529984e6acd173455954958b1b18", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-affix": "*", "kartik-v/yii2-widget-alert": "*", "kartik-v/yii2-widget-colorinput": "*", "kartik-v/yii2-widget-datepicker": "*", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widget-growl": "*", "kartik-v/yii2-widget-rangeinput": "*", "kartik-v/yii2-widget-rating": "*", "kartik-v/yii2-widget-select2": "*", "kartik-v/yii2-widget-sidenav": "*", "kartik-v/yii2-widget-spinner": "*", "kartik-v/yii2-widget-switchinput": "*", "kartik-v/yii2-widget-timepicker": "*", "kartik-v/yii2-widget-touchspin": "*", "kartik-v/yii2-widget-typeahead": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\widgets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Collection of useful widgets for Yii Framework 2.0 extending functionalities for Bootstrap", "homepage": "https://github.com/kartik-v/yii2-widgets", "keywords": ["extension", "form", "widget", "yii2"], "time": "2018-11-05T12:31:44+00:00"}, {"name": "league/flysystem", "version": "1.0.52", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "c5a5097156387970e6f0ccfcdf03f752856f3391"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/c5a5097156387970e6f0ccfcdf03f752856f3391", "reference": "c5a5097156387970e6f0ccfcdf03f752856f3391", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.10"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2019-05-20T20:21:14+00:00"}, {"name": "league/glide", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/glide.git", "reference": "a5477e9e822ed57b39861a17092b92553634932d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/glide/zipball/a5477e9e822ed57b39861a17092b92553634932d", "reference": "a5477e9e822ed57b39861a17092b92553634932d", "shasum": ""}, "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^5.5 | ^7.0", "psr/http-message": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/php-token-stream": "^1.4", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Glide\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "description": "Wonderfully easy on-demand image manipulation library with an HTTP based API.", "homepage": "http://glide.thephpleague.com", "keywords": ["ImageMagick", "editing", "gd", "image", "imagick", "league", "manipulation", "processing"], "time": "2019-04-03T23:46:42+00:00"}, {"name": "league/uri", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "f2bceb755f1108758cf4cf925e4cd7699ce686aa"}, "require": {"ext-fileinfo": "*", "ext-intl": "*", "ext-mbstring": "*", "league/uri-components": "^1.8", "league/uri-hostname-parser": "^1.1", "league/uri-interfaces": "^1.0", "league/uri-manipulations": "^1.5", "league/uri-parser": "^1.4", "league/uri-schemes": "^1.2", "php": ">=7.0.13", "psr/http-message": "^1.0"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "uri", "url", "ws"], "time": "2018-03-14T17:19:39+00:00"}, {"name": "league/uri-components", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-components.git", "reference": "d0412fd730a54a8284009664188cf239070eae64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-components/zipball/d0412fd730a54a8284009664188cf239070eae64", "reference": "d0412fd730a54a8284009664188cf239070eae64", "shasum": ""}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-intl": "*", "league/uri-hostname-parser": "^1.1.0", "php": ">=7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.3", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI components manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "time": "2018-10-24T11:31:02+00:00"}, {"name": "league/uri-hostname-parser", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-hostname-parser.git", "reference": "7a6be3d06d0ed08dcb51f666aa60f3b66cd51325"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-hostname-parser/zipball/7a6be3d06d0ed08dcb51f666aa60f3b66cd51325", "reference": "7a6be3d06d0ed08dcb51f666aa60f3b66cd51325", "shasum": ""}, "require": {"ext-intl": "*", "php": ">=7.0", "psr/simple-cache": "^1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.7", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^6.3"}, "suggest": {"ext-curl": "To use the bundle cURL HTTP client", "psr/simple-cache-implementation": "To enable using other cache providers"}, "bin": ["bin/update-psl-icann-section"], "type": "library", "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://about.me/jere<PERSON><PERSON><PERSON>l", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://nyamsprod.com", "role": "Developer"}, {"name": "Contributors", "homepage": "https://github.com/phpleague/uri-hostname-parser/graphs/contributors"}], "description": "ICANN base hostname parsing implemented in PHP.", "homepage": "https://github.com/thephphleague/uri-hostname-parser", "keywords": ["Public Suffix List", "domain parsing", "icann"], "time": "2018-02-16T07:29:26+00:00"}, {"name": "league/uri-interfaces", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "081760c53a4ce76c9935a755a21353610f5495f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/081760c53a4ce76c9935a755a21353610f5495f6", "reference": "081760c53a4ce76c9935a755a21353610f5495f6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interface for URI representation", "homepage": "http://github.com/thephpleague/uri-interfaces", "keywords": ["rfc3986", "rfc3987", "uri", "url"], "time": "2018-11-05T14:00:06+00:00"}, {"name": "league/uri-manipulations", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-manipulations.git", "reference": "ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-manipulations/zipball/ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2", "reference": "ae8d49a3203ccf7a1e39aaf7fae9f08bfbc454a2", "shasum": ""}, "require": {"ext-intl": "*", "league/uri-components": "^1.8.0", "league/uri-interfaces": "^1.0", "php": ">=7.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "guzzlehttp/psr7": "^1.2", "league/uri-schemes": "^1.2", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0", "zendframework/zend-diactoros": "1.4.0"}, "suggest": {"league/uri-schemes": "Allow manipulating URI objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://url.thephpleague.com", "keywords": ["formatter", "manipulation", "manipulations", "middlewares", "modifiers", "psr-7", "references", "rfc3986", "rfc3987", "uri", "url"], "time": "2018-03-14T16:44:57+00:00"}, {"name": "league/uri-parser", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-parser.git", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/671548427e4c932352d9b9279fdfa345bf63fa00", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-schemes": "Allow validating and normalizing URI parsing results"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "userland URI parser RFC 3986 compliant", "homepage": "https://github.com/thephpleague/uri-parser", "keywords": ["parse_url", "parser", "rfc3986", "rfc3987", "uri", "url"], "time": "2018-11-22T07:55:51+00:00"}, {"name": "league/uri-schemes", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-schemes.git", "reference": "f821a444785724bcc9bc244b1173b9d6ca4d71e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-schemes/zipball/f821a444785724bcc9bc244b1173b9d6ca4d71e6", "reference": "f821a444785724bcc9bc244b1173b9d6ca4d71e6", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/uri-interfaces": "^1.1", "league/uri-parser": "^1.4.0", "php": ">=7.0.13", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-manipulations": "Needed to easily manipulate URI objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["data-uri", "file", "ftp", "http", "https", "parse_url", "psr-7", "rfc3986", "uri", "url", "ws", "wss"], "time": "2018-11-26T08:09:30+00:00"}, {"name": "mpdf/mpdf", "version": "v7.1.9", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb", "reference": "a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|9.99.99", "php": "^5.6 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0", "psr/log": "^1.0", "setasign/fpdi": "1.6.*"}, "require-dev": {"mockery/mockery": "^0.9.5", "phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^2.7.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "extra": {"branch-alias": {"dev-development": "7.x-dev"}}, "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "time": "2019-02-06T13:32:19+00:00"}, {"name": "myclabs/deep-copy", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "shasum": ""}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2019-04-07T13:18:21+00:00"}, {"name": "npm-asset/admin-lte", "version": "2.3.11", "dist": {"type": "tar", "url": "https://registry.npmjs.org/admin-lte/-/admin-lte-2.3.11.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/bootstrap", "version": "4.3.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/bootstrap/-/bootstrap-4.3.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/flot", "version": "0.8.3", "dist": {"type": "tar", "url": "https://registry.npmjs.org/flot/-/flot-0.8.3.tgz"}, "type": "npm-asset"}, {"name": "npm-asset/font-awesome", "version": "5.8.2", "source": {"type": "git", "url": "**************:FortAwesome/Font-Awesome.git", "reference": "e05a1219784935ff5beb6bfb17cdc86bc848d0bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/e05a1219784935ff5beb6bfb17cdc86bc848d0bb", "reference": "e05a1219784935ff5beb6bfb17cdc86bc848d0bb"}, "type": "npm-asset"}, {"name": "npm-asset/html5shiv", "version": "3.7.3", "dist": {"type": "tar", "url": "https://registry.npmjs.org/html5shiv/-/html5shiv-3.7.3.tgz"}, "type": "npm-asset"}, {"name": "npm-asset/jquery", "version": "3.4.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/jquery/-/jquery-3.4.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/jquery-slimscroll", "version": "1.3.8", "dist": {"type": "tar", "url": "https://registry.npmjs.org/jquery-slimscroll/-/jquery-slimscroll-1.3.8.tgz"}, "require": {"npm-asset/jquery": ">=1.7"}, "type": "npm-asset"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/0464787bfa7cd13576c5a1e318709768798bec6a", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "time": "2016-04-07T12:29:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2018-11-20T15:27:04+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2016-02-11T07:05:27+00:00"}, {"name": "setasign/fpdi", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "shasum": ""}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use \"tecnickcom/tcpdf\" as an alternative there's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF."}, "type": "library", "autoload": {"classmap": ["filters/", "fpdi.php", "fpdf_tpl.php", "fpdi_pdf_parser.php", "pdf_context.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "time": "2017-05-11T14:25:49+00:00"}, {"name": "studio-42/elfinder", "version": "2.1.49", "source": {"type": "git", "url": "https://github.com/Studio-42/elFinder.git", "reference": "8669ee276693177e2c288d852923f4a0371d1b96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Studio-42/elFinder/zipball/8669ee276693177e2c288d852923f4a0371d1b96", "reference": "8669ee276693177e2c288d852923f4a0371d1b96", "shasum": ""}, "require": {"php": ">=5.2"}, "suggest": {"barryvdh/elfinder-flysystem-driver": "VolumeDriver for elFinder to use Flysystem as a root.", "google/apiclient": "VolumeDriver GoogleDrive require `google/apiclient:^2.0.", "kunalvarma05/dropbox-php-sdk": "VolumeDriver `Dropbox`2 require `kunalvarma05/dropbox-php-sdk.", "nao-pon/flysystem-google-drive": "require in GoogleDrive network volume mounting with Flysystem."}, "type": "library", "autoload": {"classmap": ["php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "Troex Nevelin", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "Community contributions", "homepage": "https://github.com/Studio-42/elFinder/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://xoops.hypweb.net"}], "description": "File manager for web", "homepage": "http://elfinder.org", "time": "2019-04-16T15:16:44+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.1", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a", "reference": "5397cd05b0a0f7937c47b0adcb4c60e5ab936b6a", "shasum": ""}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2019-04-21T09:21:45+00:00"}, {"name": "symfony/finder", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176", "reference": "b3d4f4c0e4eadfdd8b296af9ca637cfbf51d8176", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2019-05-26T20:47:49+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.28", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "677ae5e892b081e71a665bfa7dd90fe61800c00e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/677ae5e892b081e71a665bfa7dd90fe61800c00e", "reference": "677ae5e892b081e71a665bfa7dd90fe61800c00e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2019-05-27T05:50:24+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "82ebae02209c21113908c229e9883c419720738a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/82ebae02209c21113908c229e9883c419720738a", "reference": "82ebae02209c21113908c229e9883c419720738a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "f037ea22acfaee983e271dd9c3b8bb4150bd8ad7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/f037ea22acfaee983e271dd9c3b8bb4150bd8ad7", "reference": "f037ea22acfaee983e271dd9c3b8bb4150bd8ad7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c766e95bec706cdd89903b1eda8afab7d7a6b7af", "reference": "c766e95bec706cdd89903b1eda8afab7d7a6b7af", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.9"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2019-03-04T13:44:35+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fe5e94c604826c35a32fa832f35bd036b6799609", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "bc4858fb611bda58719124ca079baff854149c89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/bc4858fb611bda58719124ca079baff854149c89", "reference": "bc4858fb611bda58719124ca079baff854149c89", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "ab50dcf166d5f577978419edd37aa2bb8eabce0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/ab50dcf166d5f577978419edd37aa2bb8eabce0c", "reference": "ab50dcf166d5f577978419edd37aa2bb8eabce0c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/process", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "a5e3dd4e93a364668034a3cb6efa963d0b33ab45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/a5e3dd4e93a364668034a3cb6efa963d0b33ab45", "reference": "a5e3dd4e93a364668034a3cb6efa963d0b33ab45", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2019-05-26T20:47:49+00:00"}, {"name": "thrieu/yii2-grid-view-state", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/thrieu/yii2-grid-view-state.git", "reference": "4c848082cc1ec7c17cea394beb8205a1329e6be7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thrieu/yii2-grid-view-state/zipball/4c848082cc1ec7c17cea394beb8205a1329e6be7", "reference": "4c848082cc1ec7c17cea394beb8205a1329e6be7", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"thrieu\\grid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Save filters from GridView to session, keep the filter state between pages.", "homepage": "https://github.com/thrieu/yii2-grid-view-state", "keywords": ["extension", "filter", "gridview", "widget", "yii2"], "time": "2018-11-01T08:20:51+00:00"}, {"name": "trntv/cheatsheet", "version": "0.1", "source": {"type": "git", "url": "https://github.com/trntv/cheatsheet.git", "reference": "f74e52e1cac62206ec9675ca96b8bc13c36be73d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/cheatsheet/zipball/f74e52e1cac62206ec9675ca96b8bc13c36be73d", "reference": "f74e52e1cac62206ec9675ca96b8bc13c36be73d", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"cheatsheet\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cheat sheet of knowledge you may use in your code", "time": "2015-02-03T00:33:00+00:00"}, {"name": "trntv/probe", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/trntv/probe.git", "reference": "286cb379ef6e4da67fe03ef118415dcd3ee2d439"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/probe/zipball/286cb379ef6e4da67fe03ef118415dcd3ee2d439", "reference": "286cb379ef6e4da67fe03ef118415dcd3ee2d439", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "autoload": {"psr-4": {"Probe\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "System information provider", "time": "2018-05-04T09:06:42+00:00"}, {"name": "trntv/sitemaped", "version": "0.1.2", "source": {"type": "git", "url": "https://github.com/trntv/sitemaped.git", "reference": "339c6010b8f93ebdf2bd76b2e910539058b2052d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/sitemaped/zipball/339c6010b8f93ebdf2bd76b2e910539058b2052d", "reference": "339c6010b8f93ebdf2bd76b2e910539058b2052d", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "suggest": {"ext-zlib": "@stable"}, "type": "library", "autoload": {"psr-4": {"Sitemaped\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Sitemap abstraction library", "time": "2018-11-09T14:08:49+00:00"}, {"name": "trntv/yii2-aceeditor", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/trntv/yii2-aceeditor.git", "reference": "807ce92d46375308b9ca55d4ceeda12ba632dfae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/yii2-aceeditor/zipball/807ce92d46375308b9ca55d4ceeda12ba632dfae", "reference": "807ce92d46375308b9ca55d4ceeda12ba632dfae", "shasum": ""}, "require": {"bower-asset/ace-builds": "^1.3.1", "yiisoft/yii2": "^2.0.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"trntv\\aceeditor\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 ajax.org Ace Editor widget", "keywords": ["aceeditor", "code editor", "yii"], "time": "2018-04-04T06:42:50+00:00"}, {"name": "trntv/yii2-command-bus", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/trntv/yii2-command-bus.git", "reference": "582f8f29c14fb612426acf931f6a2ce6ed396d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/yii2-command-bus/zipball/582f8f29c14fb612426acf931f6a2ce6ed396d09", "reference": "582f8f29c14fb612426acf931f6a2ce6ed396d09", "shasum": ""}, "require": {"yiisoft/yii2": "^2.0", "yiisoft/yii2-queue": "^2.0"}, "require-dev": {"phpunit/phpunit": "^4.8", "predis/predis": "^1.0", "symfony/process": "^3.0", "yiisoft/yii2-dev": "^2.0"}, "suggest": {"symfony/process": "^3.0", "yiisoft/yii2-queue": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"trntv\\bus\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 Command Bus extension", "keywords": ["command bus", "extension", "queue", "yii2"], "time": "2018-04-23T11:50:30+00:00"}, {"name": "trntv/yii2-datetime-widget", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/trntv/yii2-datetime-widget.git", "reference": "0340f8aec151f893604dc730624d159205cbf57f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/yii2-datetime-widget/zipball/0340f8aec151f893604dc730624d159205cbf57f", "reference": "0340f8aec151f893604dc730624d159205cbf57f", "shasum": ""}, "require": {"bower-asset/eonasdan-bootstrap-datetimepicker": "^4.17", "bower-asset/moment": "^2.21", "yiisoft/yii2": "^2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"trntv\\yii\\datetime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0+"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Date/Time Picker widget for Yii2 framework", "keywords": ["bootstrap", "datetime picker", "extension", "widget", "yii2"], "time": "2018-11-23T12:01:56+00:00"}, {"name": "trntv/yii2-glide", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/trntv/yii2-glide.git", "reference": "a2a0fed9d236ef274a8297d01d0124bf1afe1f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/trntv/yii2-glide/zipball/a2a0fed9d236ef274a8297d01d0124bf1afe1f91", "reference": "a2a0fed9d236ef274a8297d01d0124bf1afe1f91", "shasum": ""}, "require": {"league/glide": "^1.1", "league/uri": "^5.0", "php": ">=5.5.9", "symfony/http-foundation": "^3.0", "yiisoft/yii2": "^2.0.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"trntv\\glide\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 Glide Extension", "keywords": ["extension", "glide", "thumbnail", "yii2"], "time": "2018-04-26T11:16:54+00:00"}, {"name": "vision/yii2-private-messages", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/visionp/yii2-private-messages.git", "reference": "0c60248e750dc73005dd83eecc32143a129113f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/visionp/yii2-private-messages/zipball/0c60248e750dc73005dd83eecc32143a129113f8", "reference": "0c60248e750dc73005dd83eecc32143a129113f8", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "require-dev": {"yiisoft/yii2-codeception": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"vision\\messages\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Private messages.", "keywords": ["Private Messages", "extension", "messages", "yii2"], "time": "2017-01-23T14:04:45+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2a7dcf7e3e02dc5e701004e51a6f304b713107d5", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2019-01-29T11:11:52+00:00"}, {"name": "yii2-starter-kit/yii2-file-kit", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/yii2-starter-kit/yii2-file-kit.git", "reference": "94f0bdd241cebe1e04f788f30010107975a4df0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2-starter-kit/yii2-file-kit/zipball/94f0bdd241cebe1e04f788f30010107975a4df0b", "reference": "94f0bdd241cebe1e04f788f30010107975a4df0b", "shasum": ""}, "require": {"bower-asset/blueimp-file-upload": "^9.7.0", "league/flysystem": "^1.0", "yiisoft/yii2": "^2.0.13", "yiisoft/yii2-jui": "^2.0.0"}, "require-dev": {"creocoder/yii2-flysystem": "~0.8", "phpunit/phpunit": "~4.5.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"trntv\\filekit\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 file upload and storage kit", "time": "2019-02-18T13:13:55+00:00"}, {"name": "yii2mod/yii2-swagger", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/yii2mod/yii2-swagger.git", "reference": "f5bbea5b17c4aa0f5039b0b2d0c9422827a437e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2mod/yii2-swagger/zipball/f5bbea5b17c4aa0f5039b0b2d0c9422827a437e9", "reference": "f5bbea5b17c4aa0f5039b0b2d0c9422827a437e9", "shasum": ""}, "require": {"bower-asset/swagger-ui": "^3.1", "php": ">=7.1.0", "yiisoft/yii2": "~2.0.12", "zircote/swagger-php": "^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.0", "phpunit/phpunit": "~6.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"yii2mod\\swagger\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Swagger Documentation Generator for Yii2 Framework", "keywords": ["yii2", "yii2 api documentation generator", "yii2 swagger"], "time": "2017-10-19T14:33:59+00:00"}, {"name": "yii2tech/ar-softdelete", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/yii2tech/ar-softdelete.git", "reference": "3e29cb1865a7df66de42f6f888b99d2797a80204"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2tech/ar-softdelete/zipball/3e29cb1865a7df66de42f6f888b99d2797a80204", "reference": "3e29cb1865a7df66de42f6f888b99d2797a80204", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.13"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii2tech\\ar\\softdelete\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides support for ActiveRecord soft delete in Yii2", "keywords": ["active", "delete", "integrity", "record", "smart", "soft", "yii2"], "time": "2019-01-25T15:57:39+00:00"}, {"name": "yii2tech/balance", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/yii2tech/balance.git", "reference": "43c8e990b52460dce227af6d199ace6206a72afa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2tech/balance/zipball/43c8e990b52460dce227af6d199ace6206a72afa", "reference": "43c8e990b52460dce227af6d199ace6206a72afa", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.14"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii2tech\\balance\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides support for Balance accounting system based on debit and credit principle", "keywords": ["Accounting", "balance", "bookkeeping", "credit", "debit", "yii2"], "time": "2018-09-19T10:14:35+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.19", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "3e74c7338dd83cea6ffc6f5c25202b9f2f271f89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/3e74c7338dd83cea6ffc6f5c25202b9f2f271f89", "reference": "3e74c7338dd83cea6ffc6f5c25202b9f2f271f89", "shasum": ""}, "require": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "http://www.yiiframework.com/", "keywords": ["framework", "yii2"], "time": "2019-05-21T16:01:16+00:00"}, {"name": "yiisoft/yii2-authclient", "version": "2.2.2", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-authclient.git", "reference": "00ae89f4b82df6380b0113557d1e9b918c243337"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-authclient/zipball/00ae89f4b82df6380b0113557d1e9b918c243337", "reference": "00ae89f4b82df6380b0113557d1e9b918c243337", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.13", "yiisoft/yii2-httpclient": "~2.0.5"}, "require-dev": {"phpunit/phpunit": "4.8.27|~5.7.21|^6.2"}, "suggest": {"spomky-labs/jose": "required for JWS,JWT or JWK related flows like OpenIDConnect"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\authclient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "External authentication via OAuth and OpenID for the Yii framework", "keywords": ["OpenID Connect", "OpenId", "api", "auth", "o<PERSON>h", "yii2"], "time": "2019-05-14T19:51:04+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "073c9ab0a4eb71f2485d84c96a1967130300d8fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/073c9ab0a4eb71f2485d84c96a1967130300d8fc", "reference": "073c9ab0a4eb71f2485d84c96a1967130300d8fc", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.4.* | 3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": "~2.0.6"}, "require-dev": {"phpunit/phpunit": "<7"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\bootstrap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "time": "2019-04-23T13:18:43+00:00"}, {"name": "yiisoft/yii2-bootstrap4", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap4.git", "reference": "149f0ff76255dcbd66bc3474efc15785824924d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap4/zipball/149f0ff76255dcbd66bc3474efc15785824924d9", "reference": "149f0ff76255dcbd66bc3474efc15785824924d9", "shasum": ""}, "require": {"npm-asset/bootstrap": "^4.2", "yiisoft/yii2": "~2.0"}, "require-dev": {"phpunit/phpunit": "<7", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii\\bootstrap4\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "bootstrap4", "yii2"], "time": "2019-04-30T19:59:00+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "1439e78be1218c492e6cde251ed87d3f128b9534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/1439e78be1218c492e6cde251ed87d3f128b9534", "reference": "1439e78be1218c492e6cde251ed87d3f128b9534", "shasum": ""}, "require": {"composer-plugin-api": "^1.0"}, "require-dev": {"composer/composer": "^1.0"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "time": "2018-07-05T15:44:47+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "d1c18f0dcbd72ab285acd320c56b1aa2554e06fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/d1c18f0dcbd72ab285acd320c56b1aa2554e06fa", "reference": "d1c18f0dcbd72ab285acd320c56b1aa2554e06fa", "shasum": ""}, "require": {"phpspec/php-diff": "^1.1.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"phpunit/phpunit": "<7", "yiisoft/yii2-coding-standards": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\gii\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "gii", "yii2"], "time": "2019-03-17T19:23:15+00:00"}, {"name": "yiisoft/yii2-httpclient", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-httpclient.git", "reference": "cf532447447216bd8cb167689ac18f4e7dc1c1a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-httpclient/zipball/cf532447447216bd8cb167689ac18f4e7dc1c1a7", "reference": "cf532447447216bd8cb167689ac18f4e7dc1c1a7", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.13"}, "require-dev": {"phpunit/phpunit": "4.8.27|~5.7.21|^6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\httpclient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP client extension for the Yii framework", "keywords": ["curl", "http", "httpclient", "yii2"], "time": "2019-05-14T13:33:37+00:00"}, {"name": "yiisoft/yii2-jui", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-jui.git", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-jui/zipball/ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "shasum": ""}, "require": {"bower-asset/jquery-ui": "~1.12.1", "yiisoft/yii2": "~2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\jui\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Jquery UI extension for the Yii framework", "keywords": ["jQuery <PERSON>", "yii2"], "time": "2017-11-25T15:32:29+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-queue.git", "reference": "f5806e3f65a9f983d54b9aa920b8c06df34dc74c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/f5806e3f65a9f983d54b9aa920b8c06df34dc74c", "reference": "f5806e3f65a9f983d54b9aa920b8c06df34dc74c", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"aws/aws-sdk-php": ">=2.4", "enqueue/amqp-lib": "^0.8||^0.9.10", "jeremeamia/superclosure": "*", "pda/pheanstalk": "v3.*", "php-amqplib/php-amqplib": "*", "phpunit/phpunit": "~4.4", "yiisoft/yii2-debug": "*", "yiisoft/yii2-gii": "*", "yiisoft/yii2-redis": "*"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\sqs\\": "src/drivers/sqs"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "time": "2019-05-21T15:47:38+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-swiftmailer.git", "reference": "09659a55959f9e64b8178d842b64a9ffae42b994"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/09659a55959f9e64b8178d842b64a9ffae42b994", "reference": "09659a55959f9e64b8178d842b64a9ffae42b994", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~6.0", "yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "time": "2018-09-23T22:00:47+00:00"}, {"name": "zircote/swagger-php", "version": "2.0.14", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "f2a00f26796e5cd08fd812275ba2db3d1e807663"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/f2a00f26796e5cd08fd812275ba2db3d1e807663", "reference": "f2a00f26796e5cd08fd812275ba2db3d1e807663", "shasum": ""}, "require": {"doctrine/annotations": "*", "php": ">=5.6", "symfony/finder": ">=2.2"}, "require-dev": {"phpunit/phpunit": ">=4.8.35 <=5.6", "squizlabs/php_codesniffer": ">=2.7", "zendframework/zend-form": "<2.8"}, "bin": ["bin/swagger"], "type": "library", "autoload": {"psr-4": {"Swagger\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "description": "Swagger-PHP - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "time": "2019-05-17T10:10:34+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "ab0a02ea14893860bca00f225f5621d351a3ad07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/ab0a02ea14893860bca00f225f5621d351a3ad07", "reference": "ab0a02ea14893860bca00f225f5621d351a3ad07", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "~4.5|~5", "symfony/phpunit-bridge": "~2.7|~3|~4", "symfony/yaml": "~2.3|~3|~4"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "time": "2019-01-16T14:22:17+00:00"}, {"name": "codeception/codeception", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "c50789a9a62cc0eefc0252e88a5f04f8c47b55f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/c50789a9a62cc0eefc0252e88a5f04f8c47b55f4", "reference": "c50789a9a62cc0eefc0252e88a5f04f8c47b55f4", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/phpunit-wrapper": "^6.0|^7.0", "codeception/stub": "^1.0", "ext-json": "*", "ext-mbstring": "*", "facebook/webdriver": ">=1.1.3 <2.0", "guzzlehttp/guzzle": ">=4.1.4 <7.0", "guzzlehttp/psr7": "~1.0", "php": ">=5.4.0 <8.0", "symfony/browser-kit": ">=2.7 <5.0", "symfony/console": ">=2.7 <5.0", "symfony/css-selector": ">=2.7 <5.0", "symfony/dom-crawler": ">=2.7 <5.0", "symfony/event-dispatcher": ">=2.7 <5.0", "symfony/finder": ">=2.7 <5.0", "symfony/yaml": ">=2.7 <5.0"}, "require-dev": {"codeception/specify": "~0.3", "facebook/graph-sdk": "~5.3", "flow/jsonpath": "~0.2", "monolog/monolog": "~1.8", "pda/pheanstalk": "~3.0", "php-amqplib/php-amqplib": "~2.4", "predis/predis": "^1.0", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <5.0", "vlucas/phpdotenv": "^2.4.0"}, "suggest": {"aws/aws-sdk-php": "For using AWS Auth in REST module and Queue module", "codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests", "codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "flow/jsonpath": "For using JSONPath in REST module", "league/factory-muffin": "For DataFactory module", "league/factory-muffin-faker": "For Faker support in DataFactory module", "phpseclib/phpseclib": "for SFTP option in FTP Module", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Codeception\\": "src\\Codeception", "Codeception\\Extension\\": "ext"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "http://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "time": "2018-02-27T00:09:12+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "7.6.1", "source": {"type": "git", "url": "https://github.com/Codeception/phpunit-wrapper.git", "reference": "ed4b12beb167dc2ecea293b4f6df6c20ce8d280f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/phpunit-wrapper/zipball/ed4b12beb167dc2ecea293b4f6df6c20ce8d280f", "reference": "ed4b12beb167dc2ecea293b4f6df6c20ce8d280f", "shasum": ""}, "require": {"phpunit/php-code-coverage": "^6.0", "phpunit/phpunit": ">=7.1 <7.6", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0"}, "require-dev": {"codeception/specify": "*", "vlucas/phpdotenv": "^2.4"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src\\"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHPUnit classes used by Codeception", "time": "2019-01-13T10:34:39+00:00"}, {"name": "codeception/stub", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "681b62348837a5ef07d10d8a226f5bc358cc8805"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/681b62348837a5ef07d10d8a226f5bc358cc8805", "reference": "681b62348837a5ef07d10d8a226f5bc358cc8805", "shasum": ""}, "require": {"phpunit/phpunit-mock-objects": ">2.3 <7.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <8.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "time": "2018-05-17T09:31:08+00:00"}, {"name": "codeception/verify", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "shasum": ""}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"files": ["src/Codeception/function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD assertion library for PHPUnit", "time": "2017-01-09T10:58:51+00:00"}, {"name": "doctrine/instantiator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "a2c590166b2133a4633738648b6b064edae0814a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/a2c590166b2133a4633738648b6b064edae0814a", "reference": "a2c590166b2133a4633738648b6b064edae0814a", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-03-17T17:37:11+00:00"}, {"name": "facebook/webdriver", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/facebook/php-webdriver.git", "reference": "bd8c740097eb9f2fc3735250fc1912bc811a954e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/php-webdriver/zipball/bd8c740097eb9f2fc3735250fc1912bc811a954e", "reference": "bd8c740097eb9f2fc3735250fc1912bc811a954e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-zip": "*", "php": "^5.6 || ~7.0", "symfony/process": "^2.8 || ^3.1 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "jakub-onderka/php-parallel-lint": "^0.9.2", "php-coveralls/php-coveralls": "^2.0", "php-mock/php-mock-phpunit": "^1.1", "phpunit/phpunit": "^5.7", "sebastian/environment": "^1.3.4 || ^2.0 || ^3.0", "squizlabs/php_codesniffer": "^2.6", "symfony/var-dumper": "^3.3 || ^4.0"}, "suggest": {"ext-SimpleXML": "For Firefox profile creation"}, "type": "library", "extra": {"branch-alias": {"dev-community": "1.5-dev"}}, "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "A PHP client for Selenium WebDriver", "homepage": "https://github.com/facebook/php-webdriver", "keywords": ["facebook", "php", "selenium", "webdriver"], "time": "2018-05-16T17:37:13+00:00"}, {"name": "fzaninotto/faker", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/f72816b43e74063c8b10357394b6bba8cb1c10de", "reference": "f72816b43e74063c8b10357394b6bba8cb1c10de", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2018-07-12T10:23:15+00:00"}, {"name": "phar-io/manifest", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/2df402786ab5368a0169091f61a7c1e0eb6852d0", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^1.0.1", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2017-03-05T18:14:27+00:00"}, {"name": "phar-io/version", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/a70c0ced4be299a63d32fa96d9281d03e94041df", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2017-03-05T17:38:23+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c", "reference": "bdd9f737ebc2a01c06ea7ff4308ec6697db9b53c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "~1.0.5", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2019-04-30T17:48:53+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-07-14T14:27:02+00:00"}, {"name": "phpspec/prophecy", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2018-08-05T17:53:17+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "4cab20a326d14de7575a8e235c70d879b569a57a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4cab20a326d14de7575a8e235c70d879b569a57a", "reference": "4cab20a326d14de7575a8e235c70d879b569a57a", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-05-28T11:49:20+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "8b389aebe1b8b0578430bda0c7c95a829608e059"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/8b389aebe1b8b0578430bda0c7c95a829608e059", "reference": "8b389aebe1b8b0578430bda0c7c95a829608e059", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-02-20T10:12:59+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "c99e3be9d3e85f60646f152f9002d46ed7770d18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/c99e3be9d3e85f60646f152f9002d46ed7770d18", "reference": "c99e3be9d3e85f60646f152f9002d46ed7770d18", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2018-10-30T05:52:18+00:00"}, {"name": "phpunit/phpunit", "version": "7.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "ca64dba53b88aba6af32aebc6b388068db95c435"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/ca64dba53b88aba6af32aebc6b388068db95c435", "reference": "ca64dba53b88aba6af32aebc6b388068db95c435", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.6.1", "phar-io/manifest": "^1.0.1", "phar-io/version": "^1.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.1", "phpunit/php-file-iterator": "^1.4.3", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.0", "phpunit/phpunit-mock-objects": "^6.1.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^3.1", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^1.0", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2018-04-29T15:09:19+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "6.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "f9756fd4f43f014cb2dca98deeaaa8ce5500a36e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/f9756fd4f43f014cb2dca98deeaaa8ce5500a36e", "reference": "f9756fd4f43f014cb2dca98deeaaa8ce5500a36e", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.5", "php": "^7.1", "phpunit/php-text-template": "^1.2.1", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2018-05-29T13:54:20+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2017-07-01T08:51:00+00:00"}, {"name": "sebastian/exporter", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "234199f4528de6d12aaa58b612e98f7d36adb937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/234199f4528de6d12aaa58b612e98f7d36adb937", "reference": "234199f4528de6d12aaa58b612e98f7d36adb937", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2017-04-03T13:19:02+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/browser-kit", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "3fa7d8cbd2e5006038a09b8ef93f3859a89b627e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/3fa7d8cbd2e5006038a09b8ef93f3859a89b627e", "reference": "3fa7d8cbd2e5006038a09b8ef93f3859a89b627e", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "~3.4|~4.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0", "symfony/http-client": "^4.3", "symfony/mime": "^4.3", "symfony/process": "~3.4|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2019-04-15T20:15:25+00:00"}, {"name": "symfony/console", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "707b619d2c3bedf0224d56f95f77dabc60102305"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/707b619d2c3bedf0224d56f95f77dabc60102305", "reference": "707b619d2c3bedf0224d56f95f77dabc60102305", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/var-dumper": "^4.3"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2019-05-27T08:16:38+00:00"}, {"name": "symfony/css-selector", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "105c98bb0c5d8635bea056135304bd8edcc42b4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/105c98bb0c5d8635bea056135304bd8edcc42b4d", "reference": "105c98bb0c5d8635bea056135304bd8edcc42b4d", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2019-01-16T21:53:39+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "28edb1d371640654fbfb9df53d70fa03fdf69fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/28edb1d371640654fbfb9df53d70fa03fdf69fb6", "reference": "28edb1d371640654fbfb9df53d70fa03fdf69fb6", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "~3.4|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2019-04-26T05:53:56+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "c71314cd3b9420b732e1526f33a24eff5430b5b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c71314cd3b9420b732e1526f33a24eff5430b5b3", "reference": "c71314cd3b9420b732e1526f33a24eff5430b5b3", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/expression-language": "~3.4|~4.0", "symfony/http-foundation": "^3.4|^4.0", "symfony/service-contracts": "^1.1", "symfony/stopwatch": "~3.4|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2019-05-28T07:50:59+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "8fa2cf2177083dd59cf8e44ea4b6541764fbda69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8fa2cf2177083dd59cf8e44ea4b6541764fbda69", "reference": "8fa2cf2177083dd59cf8e44ea4b6541764fbda69", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-05-22T12:23:29+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "d1fb4abcc0c47be136208ad9d68bf59f1ee17abd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/d1fb4abcc0c47be136208ad9d68bf59f1ee17abd", "reference": "d1fb4abcc0c47be136208ad9d68bf59f1ee17abd", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/191afdcb5804db960d26d8566b7e9a2843cab3a0", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/container": "", "symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-05-28T07:50:59+00:00"}, {"name": "symfony/yaml", "version": "v4.3.0", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "c60ecf5ba842324433b46f58dc7afc4487dbab99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/c60ecf5ba842324433b46f58dc7afc4487dbab99", "reference": "c60ecf5ba842324433b46f58dc7afc4487dbab99", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2019-04-06T14:04:46+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/1c42705be2b6c1de5904f8afacef5895cab44bf8", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-04-04T09:56:43+00:00"}, {"name": "webmozart/assert", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/83e253c8e0be5b0257b881e1827274667c5c17a9", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2018-12-25T11:19:39+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "4cc407f34fd90e410d806ea3264781a9fb4fd1ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/4cc407f34fd90e410d806ea3264781a9fb4fd1ef", "reference": "4cc407f34fd90e410d806ea3264781a9fb4fd1ef", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"phpunit/phpunit": "<7", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2019-05-14T13:39:56+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "3df62b1dcb272a8413f9c6e532c9d73f325ccde1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/3df62b1dcb272a8413f9c6e532c9d73f325ccde1", "reference": "3df62b1dcb272a8413f9c6e532c9d73f325ccde1", "shasum": ""}, "require": {"fzaninotto/faker": "~1.4", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\faker\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "time": "2018-02-19T20:27:10+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"asofter/yii2-imperavi-redactor": 20, "trntv/yii2-datetime-widget": 20, "trntv/cheatsheet": 20, "npm-asset/flot": 15, "kartik-v/yii2-widgets": 20, "kartik-v/yii2-bootstrap4-dropdown": 20, "thrieu/yii2-grid-view-state": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.1.0", "ext-intl": "*"}, "platform-dev": []}