<?php

declare(strict_types=1);

namespace tests\unit\alientech\Domain\Shared;

use common\chip\externalIntegrations\alientech\Domain\Shared\ReadingType;
use PHPUnit\Framework\TestCase;

/**
 * Тесты для ReadingType
 */
final class ReadingTypeTest extends TestCase
{
    public function testCreateObd(): void
    {
        $readingType = ReadingType::obd();
        
        $this->assertTrue($readingType->isObd());
        $this->assertFalse($readingType->isBoot());
        $this->assertEquals('obd', $readingType->getValue());
    }

    public function testCreateBoot(): void
    {
        $readingType = ReadingType::boot();
        
        $this->assertTrue($readingType->isBoot());
        $this->assertFalse($readingType->isObd());
        $this->assertEquals('boot', $readingType->getValue());
    }

    public function testFromReadMethodId(): void
    {
        $obdType = ReadingType::fromReadMethodId(58);
        $this->assertTrue($obdType->isObd());

        $bootType = ReadingType::fromReadMethodId(59);
        $this->assertTrue($bootType->isBoot());
    }

    public function testFromReadMethodIdThrowsExceptionForInvalidId(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported readmethod_id: 999');
        
        ReadingType::fromReadMethodId(999);
    }

    public function testGetEncodingOperationType(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals(6, $obdType->getEncodingOperationType());

        $bootType = ReadingType::boot();
        $this->assertEquals(7, $bootType->getEncodingOperationType());
    }

    public function testGetEncodingEndpoint(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals('/api/kess3/encode-obd/{customerCode}', $obdType->getEncodingEndpoint());

        $bootType = ReadingType::boot();
        $this->assertEquals('/api/kess3/encode-boot/{customerCode}', $bootType->getEncodingEndpoint());
    }

    public function testGetEncodingCallbackPath(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals('/api/kess3-encoded-obd', $obdType->getEncodingCallbackPath());

        $bootType = ReadingType::boot();
        $this->assertEquals('/api/kess3-encoded-boot', $bootType->getEncodingCallbackPath());
    }

    public function testGetValidFileTypes(): void
    {
        $obdType = ReadingType::obd();
        $obdFileTypes = $obdType->getValidFileTypes();
        $this->assertContains('OBDDecoded', $obdFileTypes);
        $this->assertContains('OBDModified', $obdFileTypes);

        $bootType = ReadingType::boot();
        $bootFileTypes = $bootType->getValidFileTypes();
        $this->assertContains('BootBenchDecodedMicro', $bootFileTypes);
        $this->assertContains('BootBenchModifiedFlash', $bootFileTypes);
    }

    public function testGetEstimatedDuration(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals(180, $obdType->getEstimatedDuration());

        $bootType = ReadingType::boot();
        $this->assertEquals(240, $bootType->getEstimatedDuration());
    }

    public function testGetTimeout(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals(1200, $obdType->getTimeout());

        $bootType = ReadingType::boot();
        $this->assertEquals(1500, $bootType->getTimeout());
    }

    public function testEquals(): void
    {
        $obd1 = ReadingType::obd();
        $obd2 = ReadingType::obd();
        $boot = ReadingType::boot();

        $this->assertTrue($obd1->equals($obd2));
        $this->assertFalse($obd1->equals($boot));
    }

    public function testToString(): void
    {
        $obdType = ReadingType::obd();
        $this->assertEquals('obd', (string) $obdType);

        $bootType = ReadingType::boot();
        $this->assertEquals('boot', (string) $bootType);
    }

    public function testInvalidTypeThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid reading type: invalid');
        
        new ReadingType('invalid');
    }
}
