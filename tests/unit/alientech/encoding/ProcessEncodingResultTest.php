<?php

declare(strict_types=1);

namespace tests\unit\alientech\encoding;

use common\chip\externalIntegrations\alientech\Application\Encoding\Command\ProcessEncodingResultCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\ProcessEncodingResultHandler;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Application\Encoding\EventHandler\EncodingCompletedEventHandler;
use common\chip\event\EventDispatcher;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Тест для ProcessEncodingResultHandler
 */
class ProcessEncodingResultTest extends TestCase
{
    private ProcessEncodingResultHandler $handler;
    private EncodingRepositoryInterface|MockObject $repository;
    private EventDispatcher|MockObject $eventDispatcher;
    private EncodingCompletedEventHandler|MockObject $encodingCompletedEventHandler;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(EncodingRepositoryInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcher::class);
        $this->encodingCompletedEventHandler = $this->createMock(EncodingCompletedEventHandler::class);

        $this->handler = new ProcessEncodingResultHandler(
            $this->repository,
            $this->eventDispatcher,
            $this->encodingCompletedEventHandler
        );
    }

    public function testProcessEncodingResultSuccess(): void
    {
        // Arrange
        $externalOperationId = 'test-operation-123';
        $result = [
            'encodedFileUrls' => [
                'OBDEncoded' => 'https://api.alientech.to/download/encoded-file-1'
            ]
        ];

        $command = new ProcessEncodingResultCommand(
            externalOperationId: $externalOperationId,
            isCompleted: true,
            isSuccessful: true,
            result: $result
        );

        $operation = $this->createMock(EncodingOperation::class);
        $operation->expects($this->once())
            ->method('complete')
            ->with($result);

        $operation->expects($this->any())
            ->method('getOperationId')
            ->willReturn($this->createMock(\common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\OperationId::class));

        $operation->expects($this->any())
            ->method('getProjectId')
            ->willReturn($this->createMock(\common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\ProjectId::class));

        $operation->expects($this->any())
            ->method('getFiles')
            ->willReturn([]);

        $this->repository->expects($this->once())
            ->method('findByExternalOperationId')
            ->with($externalOperationId)
            ->willReturn($operation);

        $this->repository->expects($this->once())
            ->method('save')
            ->with($operation);

        $this->eventDispatcher->expects($this->once())
            ->method('dispatchAsync');

        $this->encodingCompletedEventHandler->expects($this->once())
            ->method('handle');

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertSame($operation, $result);
    }

    public function testProcessEncodingResultOperationNotFound(): void
    {
        // Arrange
        $externalOperationId = 'non-existent-operation';
        $command = new ProcessEncodingResultCommand(
            externalOperationId: $externalOperationId,
            isCompleted: true,
            isSuccessful: true,
            result: []
        );

        $this->repository->expects($this->once())
            ->method('findByExternalOperationId')
            ->with($externalOperationId)
            ->willReturn(null);

        // Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage("Encoding operation not found for external ID: {$externalOperationId}");

        // Act
        $this->handler->handle($command);
    }

    public function testProcessEncodingResultFailure(): void
    {
        // Arrange
        $externalOperationId = 'test-operation-123';
        $error = ['message' => 'Encoding failed', 'code' => 500];

        $command = new ProcessEncodingResultCommand(
            externalOperationId: $externalOperationId,
            isCompleted: true,
            isSuccessful: false,
            error: $error
        );

        $operation = $this->createMock(EncodingOperation::class);
        $operation->expects($this->once())
            ->method('fail')
            ->with($error);

        $operation->expects($this->any())
            ->method('getOperationId')
            ->willReturn($this->createMock(\common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\OperationId::class));

        $operation->expects($this->any())
            ->method('getProjectId')
            ->willReturn($this->createMock(\common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject\ProjectId::class));

        $operation->expects($this->any())
            ->method('getFiles')
            ->willReturn([]);

        $this->repository->expects($this->once())
            ->method('findByExternalOperationId')
            ->with($externalOperationId)
            ->willReturn($operation);

        $this->repository->expects($this->once())
            ->method('save')
            ->with($operation);

        $this->eventDispatcher->expects($this->once())
            ->method('dispatchAsync');

        // EncodingCompletedEventHandler не должен вызываться при ошибке
        $this->encodingCompletedEventHandler->expects($this->never())
            ->method('handle');

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertSame($operation, $result);
    }
}
