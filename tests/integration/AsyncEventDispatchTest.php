<?php

namespace tests\integration;


use common\chip\event\EventHandler;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\DecodingFacade;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\EncodingFacade;
use common\helpers\ProjectHelper;
use common\models\notification\Notification;
use common\models\ProjectFiles;
use common\models\Projects;
use Yii;
use yii\queue\file\Queue;

/**
 * Тесты для асинхронной отправки событий
 */
class AsyncEventDispatchTest extends NotificationSystemTest
{
    /**
     * @var Queue
     */
    protected $testQueue;
    
    /**
     * @var array
     */
    protected $pushedJobs = [];
    
    /**
     * Настройка перед каждым тестом
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем тестовую очередь
        $this->testQueue = new Queue();
        $this->testQueue->path = Yii::getAlias('@runtime/queue');
        
        // Очищаем список отправленных задач
        $this->pushedJobs = [];
        
        // Устанавливаем функцию для отправки в очередь
        $this->eventDispatcher->setQueuePusher(function ($job) {
            $this->pushedJobs[] = $job;
            return count($this->pushedJobs);
        });
    }
    
    /**
     * Тест асинхронной отправки события
     */
    public function testAsyncEventDispatch()
    {
//        $notification = Notification::findOne(27);
//        $router = Yii::$container->get('common\chip\event\routing\NotificationRouter');
//        $router->route($notification);

//        $event = $this->eventFactory->createOptionsRequestEvent($this->testProject);
//        $this->eventDispatcher->dispatch($event, 'options.requested', $this->testProject->id, $this->testUser->id);
//=============================================================================

//        $event = $this->eventFactory->createProjectCreatedEvent($this->modelForm);
//        $this->eventDispatcher->dispatch($event, 'project.created', 0, $this->testUser->id);
        $this->testProject = Projects::find()->orderBy(['id' => SORT_DESC])->one();
        $this->testFile = ProjectFiles::find()->where(['project_id' => $this->testProject->id])->one();
//=============================================================================

// Запуск декодирования
//        $facade = Yii::$container->get(DecodingFacade::class);
//
//
//        $operation = $facade->startDecoding(
//            projectId: $this->testProject->id,
//            fileId: $this->testFile->id,
//            filePath: $this->testFile->path
//        );



//
//// Получение статистики
//        $stats = $facade->getProjectStatistics($this->testProject->id);
//
//// Получение операций проекта
//        $operations = $facade->getProjectOperations($this->testProject->id);
//=============================================================================
//        $repository = Yii::$container->get(DecodingOperationRepositoryInterface::class);
//        $operation = $repository->findById(new OperationId('oDSGULz8lfj_c7nzXe7LiRsZgQjnNBkDGtdp'));
//        $event = new DecodingCompletedEvent(
//            operationId: $operation->getOperationId()->getValue(),
//            projectId: $operation->getProjectId()->getValue(),
//            fileId: $operation->getFileId()->getValue(),
//            externalOperationId: $operation->getExternalOperationId() ?? '',
//            result: $operation->getResult() ?? []
//        );
//
//        $this->eventDispatcher->dispatch(
//            event: $event,
//            entityType: 'kess3_decoding',
//            entityId: $operation->getFileId()->getValue()
//        );
//=============================================================================
//        $files = ProjectFiles::find()->where(['project_id' => $this->testProject->id])->select('id')->column();
//        $command = new StartEncodingCommand232332232(
//            projectId: $this->testProject->id,
//            fileIds: $files,
//            userId: $this->testProject->created_by,
//            callbackUrl: '/api/kess3-encoded',
//            additionalData: []
//        );
//
//        // Выполняем команду
//        $operationId = $this->startEncodingHandler->handle($command);

//=============================================================================
        $facade = Yii::$container->get(EncodingFacade::class);

        $filesArray = ProjectFiles::find()->where(['project_id' => $this->testProject->id, 'orig' => ProjectHelper::PROJECT_FILE_MODIFIED_SLAVE, 'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED])->select(['id', 'path'])->asArray()->all();
        $files = array_column($filesArray, 'id');
        $filePaths = array_column($filesArray, 'path');
        $callbackUrl = '/api/fake-encoded';
// Запуск энкодинга
        $result = $facade->startEncoding(
            projectId: $this->testProject->id,
            fileId: $this->testFile->id,
            readMethodId: $this->testProject->readmethod_id,
            files: $files,
            filePaths: $filePaths,
            callbackUrl: $callbackUrl
        );

        $notification = Notification::findOne(23);
//        $router = Yii::$container->get('common\chip\event\routing\NotificationRouter');
//        $router->route($notification);

//        $file = ProjectFiles::find()->where(['id' => 350])->one();
//
//        $fileEventFactory = new FileEventFactory();
//        $fileEvent = $fileEventFactory->createFileUploadedEvent($file);
//
//        $this->eventDispatcher->dispatch($fileEvent, 'file.uploaded');


        // Создаем событие
//        $event = $this->createProjectCreatedEvent();
//
//        // Отправляем событие асинхронно
//        $jobId = $this->eventDispatcher->dispatchAsync($event, 'project', $this->testProject->id);
//
//        // Проверяем, что задача была отправлена в очередь
//        $this->assertCount(1, $this->pushedJobs, 'Задача не была отправлена в очередь');
//        $this->assertEquals(1, $jobId, 'Неверный ID задачи');
//
//        // Проверяем, что задача имеет правильный тип
//        $job = $this->pushedJobs[0];
//        $this->assertInstanceOf(DispatchEventJob::class, $job, 'Задача имеет неверный тип');
//
//        // Проверяем, что событие было записано в журнал
//        $eventLog = $this->assertEventLogged($event, 'project', $this->testProject->id);
//
//        // Проверяем, что ID события в журнале совпадает с ID в задаче
//        $this->assertEquals($eventLog->id, $job->eventLogId, 'ID события в журнале не совпадает с ID в задаче');
    }
    
    /**
     * Тест выполнения асинхронной задачи
     */
    public function testAsyncJobExecution()
    {
        // Создаем тестовый обработчик
        $testHandler = $this->createTestHandler();
        
        // Регистрируем обработчик в контейнере
        Yii::$container->set(EventHandler::class, function () use ($testHandler) {
            return $testHandler;
        });
        
        // Регистрируем диспетчер в контейнере
        Yii::$container->set('common\chip\event\EventDispatcher', $this->eventDispatcher);
        
        // Создаем событие
        $event = $this->createProjectCreatedEvent();
        
        // Отправляем событие асинхронно
        $this->eventDispatcher->dispatchAsync($event, 'project', $this->testProject->id);
        
        // Получаем задачу
        $job = $this->pushedJobs[0];
        
        // Выполняем задачу
        $job->execute($this->testQueue);
        
        // Проверяем, что событие было записано в журнал
        $eventLog = $this->assertEventLogged($event, 'project', $this->testProject->id);
        
        // Проверяем, что событие было обработано
        $this->assertEquals(1, $eventLog->processed, 'Событие не было отмечено как обработанное');
    }
    
    /**
     * Тест обработки ошибок при выполнении асинхронной задачи
     */
    public function testAsyncJobErrorHandling()
    {
        // Создаем обработчик, который выбрасывает исключение
        $handler = $this->getMockBuilder(EventHandler::class)
            ->getMock();
        
        $handler->method('canHandle')
            ->willReturn(true);
        
        $handler->method('handle')
            ->willThrowException(new \Exception('Test async exception'));
        
        // Регистрируем обработчик в контейнере
        Yii::$container->set(EventHandler::class, function () use ($handler) {
            return $handler;
        });
        
        // Регистрируем диспетчер в контейнере
        Yii::$container->set('common\chip\event\EventDispatcher', $this->eventDispatcher);
        
        // Регистрируем репозиторий в контейнере
        Yii::$container->set('common\chip\event\repositories\EventRepository', $this->eventRepository);
        
        // Создаем событие
        $event = $this->createProjectCreatedEvent();
        
        // Отправляем событие асинхронно
        $this->eventDispatcher->dispatchAsync($event, 'project', $this->testProject->id);
        
        // Получаем задачу
        $job = $this->pushedJobs[0];
        
        // Выполняем задачу - не должно быть исключения
        $job->execute($this->testQueue);

        // Проверяем, что событие было записано в журнал
        $eventLog = $this->assertEventLogged($event, 'project', $this->testProject->id);
        
        // Проверяем, что событие было отмечено как обработанное, несмотря на ошибку
        $this->assertEquals(1, $eventLog->processed, 'Событие не было отмечено как обработанное');
        
        // Проверяем, что была записана ошибка обработки
        $this->assertNotNull($eventLog->eventHandlersLogs, 'Не записаны логи обработчиков');
        $this->assertGreaterThanOrEqual(1, count($eventLog->eventHandlersLogs), 'Должен быть хотя бы один лог обработчика');
        
        // Находим лог с ошибкой
        $errorLog = null;
        foreach ($eventLog->eventHandlersLogs as $handlerLog) {
            if (!$handlerLog->status == EventHandlersLog::STATUS_SUCCESS) {
                $errorLog = $handlerLog;
                break;
            }
        }
        
        $this->assertNotNull($errorLog, 'Не найден лог с ошибкой');
        $this->assertStringContainsString('Test async exception', $errorLog->error_message, 'Сообщение об ошибке не содержит текст исключения');
    }
}
