<?php

namespace tests\integration\kess3\interfaces;

use common\chip\externalIntegrations\kess3\Interfaces\Http\DecodingController;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use tests\integration\kess3\BaseKess3Test;
use Yii;
use yii\web\Request;
use yii\web\Response;

/**
 * Интеграционные тесты для HTTP контроллера декодирования
 */
class DecodingControllerTest extends BaseKess3Test
{
    /**
     * @var DecodingController
     */
    private $controller;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var Response  
     */
    private $response;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->controller = new DecodingController('kess3-decoding', Yii::$app);
        $this->request = Yii::$app->request;
        $this->response = Yii::$app->response;
        
        // Отключаем CSRF для тестов
        $this->controller->enableCsrfValidation = false;
    }

    /**
     * Тест callback endpoint для Alientech
     */
    public function testCallbackAction()
    {
        $this->debug("=== Testing Callback Action ===");
        
        // Сначала создаем операцию
        $testFile = $this->getRandomTestFile();
        $operation = $this->facade->startDecoding(
            projectId: $testFile->project_id,
            fileId: $testFile->id,
            filePath: $testFile->path
        );

        $externalOpId = 'ext-callback-test-' . uniqid();
        $slotId = 'slot-callback-test-' . uniqid();
        
        $operation->startDecoding($externalOpId, $slotId);
        $this->operationRepository->save($operation);

        // Подготавливаем callback данные
        $callbackData = [
            'GUID' => $externalOpId,
            'Status' => 1,
            'IsCompleted' => true,
            'IsSuccessful' => true,
            'HasFailed' => false,
            'Result' => [
                'kess3FileSlotGUID' => $slotId,
                'Name' => $testFile->filename,
                'BootBenchComponents' => [
                    [
                        'Type' => 'EEPROM',
                        'DecodedFileURL' => 'https://api.alientech.to/files/eeprom.bin'
                    ]
                ]
            ]
        ];

        // Имитируем POST запрос
        $this->mockPostRequest($callbackData);

        // Выполняем действие
        $result = $this->controller->actionCallback();

        // Проверяем ответ
        $this->assertIsArray($result);
        $this->assertArrayHasKey('status', $result);
        $this->assertEquals('ok', $result['status']);

        // Проверяем что операция была обновлена
        $updatedOperation = $this->operationRepository->findById($operation->getOperationId());
        $this->assertNotNull($updatedOperation);
        $this->assertTrue($updatedOperation->isCompleted());

        $this->debug("Callback processed successfully for operation: " . $operation->getOperationId()->getValue());
    }

    /**
     * Тест callback с ошибкой
     */
    public function testCallbackActionWithError()
    {
        $this->debug("=== Testing Callback Action With Error ===");
        
        $testFile = $this->getRandomTestFile();
        $operation = $this->facade->startDecoding(
            projectId: $testFile->project_id,
            fileId: $testFile->id,
            filePath: $testFile->path
        );

        $externalOpId = 'ext-callback-error-' . uniqid();
        $operation->startDecoding($externalOpId, 'slot-error');
        $this->operationRepository->save($operation);

        // Callback с ошибкой
        $callbackData = [
            'GUID' => $externalOpId,
            'Status' => 1,
            'IsCompleted' => true,
            'IsSuccessful' => false,
            'HasFailed' => true,
            'Error' => 'Test callback error'
        ];

        $this->mockPostRequest($callbackData);
        $result = $this->controller->actionCallback();

        $this->assertIsArray($result);
        $this->assertEquals('ok', $result['status']);

        // Проверяем что операция помечена как проваленная
        $updatedOperation = $this->operationRepository->findById($operation->getOperationId());
        $this->assertTrue($updatedOperation->isFailed());
        $this->assertEquals('Test callback error', $updatedOperation->getErrorMessage());

        $this->debug("Callback error processed successfully");
    }

    /**
     * Тест получения статуса операции
     */
    public function testStatusAction()
    {
        $this->debug("=== Testing Status Action ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();

        // Имитируем GET запрос
        $this->mockGetRequest(['operationId' => $operationId]);

        $result = $this->controller->actionStatus($operationId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('operation_id', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('project_id', $result);
        $this->assertArrayHasKey('file_id', $result);
        $this->assertArrayHasKey('created_at', $result);

        $this->assertEquals($operationId, $result['operation_id']);
        $this->assertEquals('in_progress', $result['status']);

        $this->debug("Status retrieved for operation: {$operationId}");
    }

    /**
     * Тест получения статуса несуществующей операции
     */
    public function testStatusActionNotFound()
    {
        $nonExistentId = OperationId::generate()->getValue();

        $this->mockGetRequest(['operationId' => $nonExistentId]);

        $result = $this->controller->actionStatus($nonExistentId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals('Operation not found', $result['error']);

        $this->debug("Correctly handled non-existent operation: {$nonExistentId}");
    }

    /**
     * Тест получения операций проекта
     */
    public function testProjectOperationsAction()
    {
        $this->debug("=== Testing Project Operations Action ===");
        
        $testFile = $this->getRandomTestFile();
        $projectId = $testFile->project_id;

        // Создаем несколько операций для проекта
        $operation1 = $this->createTestOperation($testFile);
        $operation2 = $this->createTestOperation($testFile);

        $this->mockGetRequest(['projectId' => $projectId]);

        $result = $this->controller->actionProjectOperations($projectId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('project_id', $result);
        $this->assertArrayHasKey('operations', $result);
        $this->assertArrayHasKey('statistics', $result);

        $this->assertEquals($projectId, $result['project_id']);
        $this->assertIsArray($result['operations']);
        $this->assertGreaterThanOrEqual(2, count($result['operations']));

        // Проверяем структуру операций
        foreach ($result['operations'] as $opData) {
            $this->assertArrayHasKey('operation_id', $opData);
            $this->assertArrayHasKey('status', $opData);
            $this->assertArrayHasKey('created_at', $opData);
        }

        $this->debug("Retrieved " . count($result['operations']) . " operations for project {$projectId}");
    }

    /**
     * Тест отмены просроченных операций
     */
    public function testCancelExpiredAction()
    {
        $this->debug("=== Testing Cancel Expired Action ===");
        
        // Создаем просроченную операцию
        $testFile = $this->getRandomTestFile();
        $operation = $this->createTestOperation($testFile);
        
        // Делаем операцию просроченной
        $reflection = new \ReflectionClass($operation);
        $createdAtProperty = $reflection->getProperty('createdAt');
        $createdAtProperty->setAccessible(true);
        
        $pastTime = new \DateTime();
        $pastTime->sub(new \DateInterval('PT2H'));
        $createdAtProperty->setValue($operation, $pastTime);
        
        $this->operationRepository->save($operation);

        // Имитируем POST запрос с параметром timeout
        $this->mockPostRequest(['timeoutMinutes' => 60]);

        $result = $this->controller->actionCancelExpired();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('cancelled_count', $result);
        $this->assertArrayHasKey('timeout_minutes', $result);

        $this->assertGreaterThanOrEqual(1, $result['cancelled_count']);
        $this->assertEquals(60, $result['timeout_minutes']);

        $this->debug("Cancelled " . $result['cancelled_count'] . " expired operations");
    }

    /**
     * Тест запуска декодирования через HTTP
     */
    public function testStartDecodingAction()
    {
        $this->debug("=== Testing Start Decoding Action ===");
        
        $testFile = $this->getRandomTestFile();

        $requestData = [
            'project_id' => $testFile->project_id,
            'file_id' => $testFile->id,
            'file_path' => $testFile->path,
            'callback_url' => 'https://test.example.com/callback'
        ];

        $this->mockPostRequest($requestData);

        $result = $this->controller->actionStartDecoding();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('operation_id', $result);
        $this->assertArrayHasKey('status', $result);

        $this->assertTrue($result['success']);
        $this->assertNotEmpty($result['operation_id']);
        $this->assertEquals('in_progress', $result['status']);

        // Проверяем что операция действительно создана
        $operationId = OperationId::fromString($result['operation_id']);
        $this->assertOperationExists($operationId);

        $this->debug("Started decoding via HTTP: " . $result['operation_id']);
    }

    /**
     * Тест валидации запроса на запуск декодирования
     */
    public function testStartDecodingValidation()
    {
        $this->debug("=== Testing Start Decoding Validation ===");
        
        // Тест с пустыми данными
        $this->mockPostRequest([]);

        $result = $this->controller->actionStartDecoding();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('errors', $result);

        $this->assertFalse($result['success']);
        $this->assertNotEmpty($result['errors']);

        $this->debug("Validation correctly rejected empty request");
    }

    /**
     * Тест получения активных операций
     */
    public function testActiveOperationsAction()
    {
        $this->debug("=== Testing Active Operations Action ===");
        
        // Создаем несколько активных операций
        $testFile = $this->getRandomTestFile();
        $op1 = $this->createTestOperation($testFile);
        $op2 = $this->createTestOperation($testFile);

        $this->mockGetRequest([]);

        $result = $this->controller->actionActiveOperations();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('operations', $result);
        $this->assertArrayHasKey('count', $result);

        $this->assertIsArray($result['operations']);
        $this->assertGreaterThanOrEqual(2, $result['count']);

        // Проверяем что все операции активные
        foreach ($result['operations'] as $opData) {
            $this->assertEquals('in_progress', $opData['status']);
        }

        $this->debug("Retrieved " . $result['count'] . " active operations");
    }

    /**
     * Тест получения системной статистики
     */
    public function testSystemStatisticsAction()
    {
        $this->debug("=== Testing System Statistics Action ===");
        
        // Создаем операции в разных состояниях
        $testFile = $this->getRandomTestFile();
        
        $inProgress = $this->createTestOperation($testFile);
        
        $completed = $this->createTestOperation($testFile);
        $completed->startDecoding('ext-completed', 'slot-completed');
        $completed->completeSuccessfully(['test' => 'url']);
        $this->operationRepository->save($completed);

        $failed = $this->createTestOperation($testFile);
        $failed->startDecoding('ext-failed', 'slot-failed');
        $failed->failWithError('Test error');
        $this->operationRepository->save($failed);

        $this->mockGetRequest([]);

        $result = $this->controller->actionSystemStatistics();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('total_operations', $result);
        $this->assertArrayHasKey('in_progress', $result);
        $this->assertArrayHasKey('completed', $result);
        $this->assertArrayHasKey('failed', $result);
        $this->assertArrayHasKey('success_rate', $result);

        $this->assertGreaterThanOrEqual(3, $result['total_operations']);
        $this->assertGreaterThanOrEqual(1, $result['in_progress']);
        $this->assertGreaterThanOrEqual(1, $result['completed']);
        $this->assertGreaterThanOrEqual(1, $result['failed']);

        $this->debug("System statistics: " . json_encode($result, JSON_PRETTY_PRINT));
    }

    /**
     * Тест обработки исключений в контроллере
     */
    public function testExceptionHandling()
    {
        $this->debug("=== Testing Exception Handling ===");
        
        // Тест с неверным operation_id
        $this->mockGetRequest(['operationId' => 'invalid-id']);

        $result = $this->controller->actionStatus('invalid-id');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('error', $result);
        $this->assertStringContains('invalid', strtolower($result['error']));

        $this->debug("Exception handling works correctly");
    }

    /**
     * Тест CORS headers
     */
    public function testCorsHeaders()
    {
        $this->debug("=== Testing CORS Headers ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();

        $this->mockGetRequest(['operationId' => $operationId]);

        // Проверяем что контроллер устанавливает CORS headers
        $this->controller->actionStatus($operationId);

        $headers = $this->response->headers;
        
        // В реальной системы должны быть установлены CORS headers
        // Здесь мы проверяем что response объект доступен
        $this->assertNotNull($headers);

        $this->debug("CORS headers handling verified");
    }

    /**
     * Имитирует POST запрос
     */
    private function mockPostRequest(array $data): void
    {
        $this->request->setBodyParams($data);
        $_POST = $data;
        
        // Имитируем POST метод
        $reflection = new \ReflectionClass($this->request);
        $methodProperty = $reflection->getProperty('_method');
        $methodProperty->setAccessible(true);
        $methodProperty->setValue($this->request, 'POST');
    }

    /**
     * Имитирует GET запрос  
     */
    private function mockGetRequest(array $params): void
    {
        $this->request->setQueryParams($params);
        $_GET = $params;
        
        // Имитируем GET метод
        $reflection = new \ReflectionClass($this->request);
        $methodProperty = $reflection->getProperty('_method');
        $methodProperty->setAccessible(true);
        $methodProperty->setValue($this->request, 'GET');
    }

    /**
     * Тест производительности контроллера
     */
    public function testControllerPerformance()
    {
        $this->debug("=== Testing Controller Performance ===");
        
        $testFile = $this->getRandomTestFile();
        $startTime = microtime(true);

        // Выполняем несколько операций подряд
        for ($i = 0; $i < 10; $i++) {
            $requestData = [
                'project_id' => $testFile->project_id,
                'file_id' => $testFile->id,
                'file_path' => $testFile->path
            ];

            $this->mockPostRequest($requestData);
            $result = $this->controller->actionStartDecoding();
            
            $this->assertTrue($result['success']);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(5.0, $executionTime, 'Controller should handle 10 requests in less than 5 seconds');
        $this->debug("Controller performance: 10 requests in {$executionTime} seconds");
    }
}
