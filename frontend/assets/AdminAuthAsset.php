<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\assets;

use yii\web\AssetBundle;

/**
 * Main application asset bundle.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class AdminAuthAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@chipassets';
    public $css = [
        'css/bootstrap.min.css',
        'css/style.css',
        'css/changes.css',
    ];
    public $js = [
        'js/popper.min.js',
        'js/bootstrap.min.js',
        'js/common-pages.js',
    ];
    public $depends = [
        'yii\web\YiiAsset',
        'yii\bootstrap\BootstrapAsset',
    ];
}
