<?php

namespace frontend\controllers;

use backend\controllers\ChipController;
use common\chip\alientech\services\AsyncOperationService;
use common\chip\alientech\services\LogService;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\DecodingFacade;
use common\helpers\AlientechApiHelper;
use common\helpers\StageHelper;
use common\models\ChipBrand;
use common\models\ChipEcu;
use common\models\ChipEcuDict;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\SeoChipPage;
use common\models\TelegramAccount;
use common\models\TelegramLog;
use common\models\User;
use frontend\models\ContactForm;
use Yii;
use yii\filters\AccessControl;
use yii\filters\Cors;
use yii\helpers\Url;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Request;
use yii\web\Response;
use function sprintf;

/**
 * @SWG\Swagger(
 *     basePath="/",
 *     produces={"application/json"},
 *     consumes={"application/x-www-form-urlencoded"},
 *     @SWG\Info(version="1.0", title="Chiptuning-ms API"),
 * )
 */
/**
 * Site controller
 */
class ApiController extends ChipController
{
    public $layout = '@backend/views/layouts/admin-clear.php';

    public $enableCsrfValidation = false;
    private $chatId = 0;

    const CLOSE_OPERATION_INTERVAL = 1;

    /**
     * @return array
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
//        $behaviors['contentNegotiator']['formats']['text/html'] = Response::FORMAT_JSON;
        $behaviors['corsFilter'] = [
            'class' => Cors::class,
            'cors' => [
                // restrict access to
                'Origin' => ['*'],
                // Allow  methods
                'Access-Control-Request-Method' => ['POST', 'PUT', 'GET', 'DELETE'],
                // Allow only headers 'X-Wsse'
                'Access-Control-Request-Headers' => ['*'],
                'Access-Control-Allow-Headers' => ['Content-Type'],
                // Allow credentials (cookies, authorization headers, etc.) to be exposed to the browser
                //'Access-Control-Allow-Credentials' => true,
                // Allow OPTIONS caching
                'Access-Control-Max-Age' => 3600,
                // Allow the X-Pagination-Current-Page header to be exposed to the browser.
                'Access-Control-Expose-Headers' => ['*'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::className(),
            'only' => ['auth-telegram', 'index', 'set-locale'],
            'rules' => [
                [
                    'actions' => ['auth-telegram', 'index', 'set-locale'],
                    'allow' => true,
                    'roles' => ['@'],
                ],
            ],
        ];
        return $behaviors;
    }

    /**
     * @inheritdoc
     */
    public function actions()
    {
        return [
//            'models' => [
//                'class' => ModelsAction::class,
//                'params' => Yii::$app->request->post()
//            ],
            'docs' => [
                'class' => 'yii2mod\swagger\SwaggerUIRenderer',
                'restUrl' => Url::to(['api/json-schema']),
            ],
            'json-schema' => [
                'class' => 'yii2mod\swagger\OpenAPIRenderer',
                // Тhe list of directories that contains the swagger annotations.
                'scanDir' => [
                    Yii::getAlias('@frontend/controllers'),
                    Yii::getAlias('@frontend/models'),
                ],
            ],
            'set-locale' => [
                'class' => 'common\actions\SetLocaleAction',
                'locales' => array_keys(Yii::$app->params['availableLocales'])
            ]
        ];
    }

    public function beforeAction($action)
    {
//        \Yii::$app->response->format = Response::FORMAT_JSON;
        return parent::beforeAction($action); // TODO: Change the autogenerated stub
    }

    /**
     * @SWG\Post(path="/api/brands",
     *     tags={"Brands"},
     *     summary="Retrieves the collection of Brand.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "Brand collection response",
     *         @SWG\Schema(ref = "#definitions/ChipBrand")
     *     )
     * )
     */
    public function actionBrands(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $brands = ChipBrand::find()->notDeleted()->select(['id', 'title'])->asArray()->all();
            return ['status'=>'success', 'content'=>$brands];
    }

    /**
     * @SWG\Post(path="/api/models-by-brand",
     *     tags={"ModelsByBrand"},
     *     summary="Retrieves the collection of Model by Brand ID.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="brand_id",
     *     description="Brand ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "Model collection response",
     *         @SWG\Schema(ref = "#definitions/ChipModel")
     *     ),
     *   @SWG\Response(response=400, description="Brand ID is required")
     * )
     */
    public function actionModelsByBrand(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $brand_id = $request->post('brand_id', 0);
        if (!empty($brand_id)) {
            $models = ChipModel::find()->notDeleted()->where(['brand_id' => $brand_id])->select(['id', 'title'])->asArray()->all();
            return ['status'=>'success', 'content'=>$models];
        } else {
            Yii::$app->getResponse()->setStatusCode(400);
            return ['status'=>'error', 'content'=>'Brand is required'];
        }
    }

    /**
     * @SWG\Post(path="/api/generations-by-model",
     *     tags={"GenerationsByModel"},
     *     summary="Retrieves the collection of Generation by Model ID.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="model_id",
     *     description="Model ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "Generation collection response",
     *         @SWG\Schema(ref = "#definitions/ChipGeneration")
     *     ),
     *   @SWG\Response(response=400, description="Model ID is required")
     * )
     */
    public function actionGenerationsByModel(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $model_id = $request->post('model_id', 0);
        if (!empty($model_id)) {
            $generations = ChipGeneration::find()->notDeleted()->where(['model_id' => $model_id])->select(['id', 'title', 'image'])->asArray()->all();
            return ['status' => 'success', 'content' => $generations];
        } else {
            Yii::$app->getResponse()->setStatusCode(400);
            return ['status'=>'error', 'content'=>'Brand is required'];
        }
    }

    /**
     * @SWG\Post(path="/api/engines-by-generation",
     *     tags={"EnginesByGeneration"},
     *     summary="Retrieves the collection of Engine by Generation ID.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="generation_id",
     *     description="Generation ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "Engine collection response",
     *         @SWG\Schema(ref = "#definitions/ChipEngine")
     *     ),
     *   @SWG\Response(response=400, description="Generation ID is required")
     * )
     */
    public function actionEnginesByGeneration(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $generation_id = $request->post('generation_id', 0);
        if (!empty($generation_id)) {
            $models = ChipEngine::find()->notDeleted()->where(['generation_id' => $generation_id])->select(['id', 'title'])->asArray()->all();
            return ['status' => 'success', 'content' => $models];
        } else {
            Yii::$app->getResponse()->setStatusCode(400);
            return ['status'=>'error', 'content'=>'Brand is required'];
        }
    }

    /**
     * @SWG\Post(path="/api/ecus-by-engine",
     *     tags={"EcusByEngine"},
     *     summary="Retrieves the collection of ECU by Engine ID.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="engine_id",
     *     description="Engine ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "ECU collection response",
     *         @SWG\Schema(ref = "#definitions/ChipEcu")
     *     ),
     *   @SWG\Response(response=400, description="Engine ID is required")
     * )
     */
    public function actionEcusByEngine(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $engine_id = $request->post('engine_id', 0);
        if (!empty($engine_id)) {
            $ecus = ChipEcuDict::find()
                ->select('chip_ecu.id as id, chip_ecu_dict.title as title')
                ->leftJoin('chip_ecu', 'chip_ecu.ecu_id = chip_ecu_dict.id')
//                ->notDeleted()
                ->where(['chip_ecu.engine_id' => $engine_id])
//                ->select(['id', 'ecu_id', 'title'])
                ->asArray()
                ->all();
            return ['status'=>'success', 'content'=>$ecus];
        } else {
            Yii::$app->getResponse()->setStatusCode(400);
            return ['status'=>'error', 'content'=>'Brand is required'];
        }
        return [];
    }

    /**
     * @SWG\Post(path="/api/summary-info",
     *     tags={"SummaryInfo"},
     *     summary="Retrieves the summary data of ECU params by Brand ID, Model ID, Generation ID, Engine ID and ECU ID.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="brand_id",
     *     description="Brand ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="model_id",
     *     description="Model ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="generation_id",
     *     description="Generation ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="engine_id",
     *     description="Engine ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="ecu_id",
     *     description="ECU ID for filtering",
     *     required=true,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *     @SWG\Response(
     *         response = 200,
     *         description = "ECU collection response"
     *     ),
     *   @SWG\Response(response=400, description="Engine ID is required")
     * )
     */
    public function actionSummaryInfo(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $brand_id = (int)$request->post('brand_id', 0);
        $model_id = (int)$request->post('model_id', 0);
        $generation_id = (int)$request->post('generation_id', 0);
        $engine_id = (int)$request->post('engine_id', 0);
        $chip_ecu_id = (int)$request->post('ecu_id', 0);

        $res = [];

        $ecu_id = 0;
        $dinostend_file = '';
        $dinostend_file_url = Url::to('/uploads/dinostend/', true);

        if (!empty($chip_ecu_id)) {
            if (($chipEcu = ChipEcu::find()->where(['id' => $chip_ecu_id])->one()) !== null) {
                $ecu_id = $chipEcu->ecu_id;
                if (!empty($chipEcu->dinostend_file)) {
                    $dinostend_file = Url::to('/uploads/dinostend/'.$chipEcu->dinostend_file, true);

                }
//                var_dump($chipEcu);
//                die;
            }
        }


//        $ecu_id = !empty($chip_ecu_id) ? (isset(ChipEcu::findOne($chip_ecu_id)->ecu_id) ? ChipEcu::findOne($chip_ecu_id)->ecu_id : 0) : 0;

//         получаем параметры данные по степени тюнинга-связка (марка-модель-поколения-двиг-ЭБУ)
//        $projectsController = new ProjectsController();
        $stagesEcuData  = StageHelper::getChipStagesEcuByParams($chip_ecu_id, $ecu_id);

//        $stagesEcuDataArray = [];
//        if (!empty($stagesEcuData['standard'])) {
//            $stagesEcuDataArray['standard'] = $stagesEcuData['standard'];
//        }
        //        $firstStageSelect = 2;
        //        if (!empty($stagesEcuData['firstStage'])) {
        //            $firstStageSelect = $stagesEcuData['firstStage']->stage_id;
        //        }
//        if (!empty($stagesEcuData['stages'])) {
//            foreach ($stagesEcuData['stages'] as $stagesEcu){
//                //                foreach ($stagesDict as $stagesDictItem){
//                //                    if (($stagesDictItem->title == 'Standard') && ($stagesDictItem->id = $stagesEcu->stage_id)) {
//                //                        $stagesEcuDataArray['standard'] = $stagesEcu;
//                //                    }
//                //                }
//                //                $stagesEcuDataArray[$stagesEcu->stage_id] = [
//                //                    'hp' => $stagesEcu->inc_hp,
//                //                    'tork' =>$stagesEcu->inc_tork,
//                //                    'tork' =>$stagesEcu->inc_tork,
//                //                ];
//                $stagesEcuDataArray[$stagesEcu->stage_id] = $stagesEcu;
//            }
//        }
        //        echo '$stagesEcuDataArray<pre>';
        //        print_r($stagesEcuDataArray);
        //        die;
        //
        //        $ecuStages = $pricesEcuData->prices;
        // получаем параметры цена-комментарий-доп параметр тюнинга-связка (марка-модель-поколения-двиг-ЭБУ)
        $additionsDataArray = StageHelper::getChipEcuAdditions($brand_id, $model_id, $generation_id, $engine_id, $ecu_id);
        $res['stages'] = $stagesEcuData;
        $res['additions'] = $additionsDataArray;
        $res['dinostend_file'] = $dinostend_file;
        $res['dinostend_file_url'] = $dinostend_file_url;
//        return $stagesEcuData;

        return ['status'=>'success', 'content'=>$res];
    }

    /**
     * @SWG\Post(path="/api/seo-chip-page",
     *     tags={"SeoChipPage"},
     *     summary="Retrieves the data of ECU params by Brand ID, Model ID, Generation ID, Engine ID and ECU ID OR by url from table seo_chip_page.",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="brand_id",
     *     description="Brand ID for filtering",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="model_id",
     *     description="Model ID for filtering",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="generation_id",
     *     description="Generation ID for filtering",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="engine_id",
     *     description="Engine ID for filtering",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="ecu_id",
     *     description="ECU ID for filtering",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="access-token",
     *     description="Access token",
     *     required=true,
     *     type="string"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="url",
     *     description="Url for search params from table seo_chip_page",
     *     required=false,
     *     type="string"
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="id",
     *     description="SEO CHIP PAGE ID for updating params",
     *     required=false,
     *     type="integer"
     *   ),
     *   @SWG\Response(
     *         response = 200,
     *         description = "ECU collection response"
     *     ),
     *   @SWG\Response(response=400, description="Access token is required"),
     *   @SWG\Response(response=404, description="SEO Page not found"),
     * )
     */

    public function actionSeoChipPage(){
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $status = 'success';
        $errors = [];
        $res = [];
        $id = (int)$request->post('id', 0);
        if (!empty($id)) {
            if (($seoChipPage = SeoChipPage::findOne($id)) == null){
                throw new NotFoundHttpException('SEO Page not found', 404);
            } else {
//                $seoPageData = $request->post('seo_page_data', []);
//                return $seoPageData;
//                return $seoPageData;
                $seoChipPage->setAttributes($request->post());
                if (!$seoChipPage->save()) {
                    $status = 'update_error';
                    $errors = $seoChipPage->getErrorSummary();
                    return ['status' => $status, 'errors' => $errors, 'content'=>$res];
                }
            }
        }
        $brand_id = (int)$request->post('brand_id', 0);
        $model_id = (int)$request->post('model_id', 0);
        $generation_id = (int)$request->post('generation_id', 0);
        $engine_id = (int)$request->post('engine_id', 0);
        $chip_ecu_id = (int)$request->post('ecu_id', 0);
        $url = $request->post('url', '');

//        var_dump($url);
//        die;
        if (!empty($chip_ecu_id)) {
            $chipEcu = ChipEcu::findOne($chip_ecu_id);
            $ecu_id = !empty($chip_ecu_id) ? (isset($chipEcu->ecu_id) ? $chipEcu->ecu_id : 0) : 0;
        }
//        return $request->post();
        if (empty($url)) {
            if (empty($chip_ecu_id)) {
                throw new NotFoundHttpException('ECU id is required attribute if url empty', 404);
            }
            if (($seoChipPage = SeoChipPage::findOne(['chip_ecu_id' => $chip_ecu_id])) !== null){
                $res['seo_page'] = $seoChipPage->attributes;
            }
        } else {

                if (($seoChipPage = SeoChipPage::findOne(['url' => $url])) !== null){
                $chip_ecu_id = $seoChipPage->chip_ecu_id;
                $ecu_id = !is_null($seoChipPage) ? (isset($seoChipPage->chip_ecu_id) ? ChipEcu::findOne($seoChipPage->chip_ecu_id)->ecu_id : 0) : 0;
                $res['seo_page'] = $seoChipPage->attributes;
            }
        }
        $chipEcu = ChipEcu::findOne($chip_ecu_id);

//         получаем параметры данные по степени тюнинга-связка (марка-модель-поколения-двиг-ЭБУ)
//        $projectsController = new ProjectsController();
        $stagesEcuData  = StageHelper::getChipStagesEcuByParams($chip_ecu_id, $ecu_id);

//        $stagesEcuDataArray = [];
//        if (!empty($stagesEcuData['standard'])) {
//            $stagesEcuDataArray['standard'] = $stagesEcuData['standard'];
//        }
        //        $firstStageSelect = 2;
        //        if (!empty($stagesEcuData['firstStage'])) {
        //            $firstStageSelect = $stagesEcuData['firstStage']->stage_id;
        //        }
//        if (!empty($stagesEcuData['stages'])) {
//            foreach ($stagesEcuData['stages'] as $stagesEcu){
//                //                foreach ($stagesDict as $stagesDictItem){
//                //                    if (($stagesDictItem->title == 'Standard') && ($stagesDictItem->id = $stagesEcu->stage_id)) {
//                //                        $stagesEcuDataArray['standard'] = $stagesEcu;
//                //                    }
//                //                }
//                //                $stagesEcuDataArray[$stagesEcu->stage_id] = [
//                //                    'hp' => $stagesEcu->inc_hp,
//                //                    'tork' =>$stagesEcu->inc_tork,
//                //                    'tork' =>$stagesEcu->inc_tork,
//                //                ];
//                $stagesEcuDataArray[$stagesEcu->stage_id] = $stagesEcu;
//            }
//        }
        //        echo '$stagesEcuDataArray<pre>';
        //        print_r($stagesEcuDataArray);
        //        die;
        //
        //        $ecuStages = $pricesEcuData->prices;
        // получаем параметры цена-комментарий-доп параметр тюнинга-связка (марка-модель-поколения-двиг-ЭБУ)
        $additionsDataArray = StageHelper::getChipEcuAdditions($brand_id, $model_id, $generation_id, $engine_id, $ecu_id);
        $res['stages'] = $stagesEcuData;
        $res['additions'] = $additionsDataArray;
        $res['params'] = [
            'brand_id' => $chipEcu->brand_id,
            'model_id' => $chipEcu->model_id,
            'generation_id' => $chipEcu->generation_id,
            'engine_id' => $chipEcu->engine_id,
            'ecu_id' => $chip_ecu_id,
        ];
//        return $stagesEcuData;

        return ['status' => $status, 'errors' => $errors, 'content'=>$res];
    }
    public function actionError()
    {
        $this->layout = '@backend/views/layouts/admin-error.php';

        $exception = Yii::$app->errorHandler->exception;
        if ($exception !== null) {
            if ($exception->statusCode == 404)
                return $this->render('error404', ['exception' => $exception]);
            else
                return $this->render('error', ['exception' => $exception]);
        }
    }
    /**
     * @return string
     */
    public function actionIndex()
    {
        //        $searchModel = new ArticleSearch();
        //        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        //        $dataProvider->sort = [
        //            'defaultOrder' => ['created_at' => SORT_DESC]
        //        ];
        //        $dataProvider->pagination = [
        //            'pageSize' => 5
        //        ];
        $this->redirect(Yii::$app->urlManagerBackend->createUrl(['timeline-event/index']));

        //        return $this->render('/timeline-event/index', [
        //            'searchModel' => $searchModel,
        //            'dataProvider' => $dataProvider,
        //        ]);
    }

    /**
     * @return string|Response
     */
    public function actionContact()
    {
        $model = new ContactForm();
        if ($model->load(Yii::$app->request->post())) {
            if ($model->contact(Yii::$app->params['adminEmail'])) {
                Yii::$app->getSession()->setFlash('alert', [
                    'body' => Yii::t('frontend', 'Thank you for contacting us. We will respond to you as soon as possible.'),
                    'options' => ['class' => 'alert-success']
                ]);
                return $this->refresh();
            }

            Yii::$app->getSession()->setFlash('alert', [
                'body' => Yii::t('frontend', 'There was an error sending email.'),
                'options' => ['class' => 'alert-danger']
            ]);
        }

        return $this->render('contact', [
            'model' => $model
        ]);
    }

    /**
     * @param string $format
     * @param bool $gzip
     * @return string
     * @throws BadRequestHttpException
     */


    public function actionTelegram() {
        //        $data = Yii::$app->request->rawBody;
        $json = file_get_contents('php://input');
        //        $request = json_decode($json);
        if (!empty($json)) {
            $tgData = json_decode($json);
            if (isset($tgData->message) && isset($tgData->message->chat)) {
                $this->chatId = $tgData->message->chat->id;
                if (($tgAcc = TelegramAccount::findOne(['chat_id' => $this->chatId])) !== null) {
                    if ($tgAcc->user && $tgAcc->user->isDeleted) {
                        $this->sendAccessDenied($this->chatId);
                        $tgAcc->delete();
                        $res = true;
                    }
                    if ($tgData->message->contact) {
                        $tgAcc->setAttributes([
                            'tg_account' => json_encode($tgData->message->contact),
                        ]);
                        $tgAcc->save(false);
                    }
                    if (empty($tgAcc->tg_account)) {
                        $this->sendContactRequest($this->chatId);
                        $res = true;
                    }
                    if ($tgAcc->user && !$tgAcc->user->isDeleted) {
                        $this->sendWellcome($this->chatId);
                        $res = true;
                    }
                } else {
                    $this->sendLoginBtn($this->chatId);
                    echo 'ok';
                    die;
                }
            }
        }
        $telegramLog = new TelegramLog();
        $telegramLog->setAttribute('data', $json);
        $telegramLog->save(false);
        Yii::$app->response->statusCode = 200;
        echo 'ok';
        die;
    }

    private function checkUser($json = '') {
        $res = false;
        if (!empty($json)) {
            $tgData = json_decode($json);
            if (isset($tgData->message) && isset($tgData->message->chat)) {
                $this->chatId = $tgData->message->chat->id;
                if(($user = User::find()->where(['tg_chat_id' => $this->chatId])->one()) !== null){
                    $res = true;
                }
            }
        }
        return $res;
    }

    public function actionSendTelegram() {
        $data = Yii::$app->telegram->sendMessage([
            'chat_id' => 579589221,
            'text' => 'this is test',
            'reply_markup' => json_encode([
                'inline_keyboard'=>[
                    [
                        [
                            'callback_data'=> time(),
                            'text'=>"Авторизоваться",
                            'url'=>Url::to(['site/auth-telegram'], true),
                            'chat_id' => 'CHAT_ID',
                        ]
                    ]
                ]
            ]),
        ]);
        //        Yii::$app->telegram->getUpdates();
//        echo '<pre>';
//        var_dump($data);
//        die;
    }

    private function sendLoginBtn($chatId)
    {
        Yii::$app->telegram->sendMessage([
            'chat_id' => $chatId,
            'text' => Yii::t('frontend','Welcome to Chiptuning-ms bot! Please login to chiptuning portal.'),
            'reply_markup' => json_encode([
                'inline_keyboard'=>[
                    [
                        [
                            //                            'callback_data'=> time(),
                            'text'=>Yii::t('frontend',"Log in"),
                            'url'=>Url::to(['site/auth-telegram', 'chat_id' => $chatId], true),
                        ]
                    ]
                ]
            ]),
        ]);
    }

    private function sendWellcome($chatId)
    {
        Yii::$app->telegram->sendMessage([
            'chat_id' => $this->chatId,
            'text' => Yii::t('frontend', 'Welcome! Now you will receive notifications from your projects.'),
        ]);
    }

    private function sendAccessDenied($chatId)
    {
        Yii::$app->telegram->sendMessage([
            'chat_id' => $this->chatId,
            'text' => Yii::t('frontend', 'Your access has been revoked, contact the portal administrator.'),
        ]);
    }

    private function sendContactRequest($chatId)
    {
        $result = [];
        $result['need_register'] = false;

        $inline_button1 = array("text" => Yii::t('frontend',"Share contact"), "request_contact" => true);
        $inline_keyboard = [[$inline_button1]];
        $keyboard=array("keyboard"=>$inline_keyboard);
        $replyMarkup = json_encode($keyboard);

        Yii::$app->telegram->sendMessage([
            'chat_id' => $this->chat->id,
            'text' => Yii::t('frontend', "We greet you, {username}! To register on the site, you must provide your contact information.", ['username' => $this->user->name]),
            'reply_markup' => $replyMarkup,
        ]);
    }

    public function actionAlien()
    {
        Yii::$app->response->format = Response::FORMAT_RAW;
        return AlientechApiHelper::authorise();
    }


    public function actionAuthTelegram() {
        $this->chatId = Yii::$app->request->get('chat_id');
        if (!empty(Yii::$app->user->identity->id)) {
            $user = User::findOne(Yii::$app->user->identity->id);
            $user->setAttribute('tg_chat_id', $this->chatId);
            $user->save(false);
            //            Yii::$app->urlManagerBackend->createUrl(['/projects/index']);
            //            var_dump(Yii::$app->urlManagerBackend->createUrl(['/projects/index']));
            //            die;
            $this->sendWellcome($this->chatId);
            $this->redirect(Yii::$app->urlManagerBackend->createUrl(['/ctadmin/projects/index']));
        } else {
            $this->redirect(Yii::$app->urlManagerBackend->createUrl(['/site/index']));
        }
        //        $json = file_get_contents('php://input');

        //        $telegramLog = new TelegramLog();
        //        $telegramLog->setAttribute('data', $json);
        //        $telegramLog->save(false);
        //        echo 'ok';
        //        die;

    }

   // -------------------------------------------------------Alientech-----------------------------------------------------------//

    /**
     * старт обработки оригинальных файлов алиентеч
     * @return array
     */
//    public function actionStartAlientechProcess():array
//    {
//        Yii::$app->response->format = Response::FORMAT_JSON;
//        $encFiles = AlientechApiHelper::getOrigEncFiles();
//        error_log(date('Y-m-d H:i:s').' --- actionStartAlientechProcess -- count($encFiles) ---'.count($encFiles)."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
//
//        if (count($encFiles) > 0) {
//            return AlientechApiHelper::startDecodeFiles($encFiles);
//        } else {
//            return ['no files'];
//        }
//        return $encFiles;
//    }

//    public function actionCheckAlientechOperations()
//    {
////        error_log(date('Y-m-d H:i:s').' --- actionCheckAlientechOperations --  ---'."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
//        Yii::$app->response->format = Response::FORMAT_JSON;
////        $projectFile = new ProjectFiles();
//        return AlientechApiHelper::checkAlientechOperations();
//    }

    // -------------------------------------------------------Alientech-----------------------------------------------------------//
    /**
     * @param Request $request
     * @param AsyncOperationService $asyncOperationService
     * @param LogService $logService
     * @return bool
     */
    public function actionKess3Decoded(Request $request, AsyncOperationService $asyncOperationService, LogService $logService): bool
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $logService->info('ApiController actionKess3Decoded');
        $data = $request->getRawBody();
        return $asyncOperationService->decoded($data);
    }

    /**
     * @param Request $request
     * @param AsyncOperationService $asyncOperationService
     * @param LogService $logService
     * @return bool
     */
    public function actionKess3Encoded(Request $request, AsyncOperationService $asyncOperationService, LogService $logService): bool
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $logService->info('ApiController actionKess3Encoded');
        $data = $request->getRawBody();
        return $asyncOperationService->encoded($data);
    }

    public function actionFakeDecoded(): Response
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $requestData = $this->getRequestData();

            if (empty($requestData)) {
                return $this->errorResponse('Empty request data', 400);
            }

            Yii::info(
                message: 'Received decoding callback: ' . json_encode($requestData),
                category: 'kess3.callback'
            );
            $decodingFacade = Yii::$container->get(DecodingFacade::class);
            $operation = $decodingFacade->processDecodingResult($requestData);

            if ($operation === null) {
                return $this->errorResponse('Operation not found', 404);
            }

            return $this->successResponse([
                'status' => 'success',
                'operation_id' => $operation->getOperationId()->getValue(),
                'external_id' => $operation->getExternalOperationId(),
                'operation_status' => $operation->getStatus()->getValue(),
            ]);
        } catch (\Exception $e) {
            Yii::error(
                message: "Error processing callback: {$e->getMessage()}",
                category: 'kess3.callback'
            );

            return $this->errorResponse($e->getMessage(), 500);
        }
    }
    private function getRequestData(): array
    {
        $rawBody = Yii::$app->request->getRawBody();

        if (!empty($rawBody)) {
            $data = json_decode($rawBody, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $data;
            }
        }

        return Yii::$app->request->post();
    }

    private function successResponse(array $data): Response
    {
        return $this->asJson($data);
    }

    private function errorResponse(string $message, int $statusCode = 400): Response
    {
        Yii::$app->response->statusCode = $statusCode;

        return $this->asJson([
            'error' => true,
            'message' => $message,
            'status_code' => $statusCode,
        ]);
    }

//    public function actionKess3StartEncode(Request $request, LogService $logService, Kess3Service $kess3Service, AsyncOperationService $asyncOperationService)
//    {
//        $logService->info('ApiController actionKess3StartEncode');
//        $projectId = $request->get('id');
////        $kess3Service->startEncoding($projectId);
////        $asyncOperationService->downloadEncodedFiles($this->operation);
//        Yii::$app->queue->push(new AlientechStartEncodingJob(['projectId' => $projectId]));
//    }

    // -------------------------------------------------------Alientech-----------------------------------------------------------//

    // -------------------------------------------------------Autopack-----------------------------------------------------------//

//    public function actionDataByJob(AutopackService $service)
//    {
////        Yii::$app->response->format = Response::FORMAT_JSON;
////        return $service->startCalculateNewScripts();
//        Yii::$app->queue->push(new AutopackStartCalculateScriptJob());
////        $service->startCalculateNewScripts();
//    }

//    public function actionModByJob(Request $request, AutopackService $service)
//    {
//        Yii::$app->response->format = Response::FORMAT_JSON;
//        $projectFileId = $request->get('id');
//        $file = ProjectFiles::findOne($projectFileId);
//        return $service->startModificationByProjectFile($file);
//    }

//    public function actionTestPack()
//    {
//        Yii::$app->response->format = Response::FORMAT_JSON;
//        $autopackLog = new AutopackLog();
//        $autopackLog->setAttribute('project_client_id', 11);
//        $autopackLog->setAttributes([
//            'autopack_result' => '75675534',
//            'file_client_orig' => 11,
//            'additions' => '234',
//        ]);
//        $autopackLog->save();

//        return Yii::$app->commandBus->handle(new AutopackProjectsCommand([
//            'project_id' => 1840,
//        ]));

//        $projects = Projects::find()->all();
//        foreach ($projects as $project) {
//            if ($project->fileOriginal && $project->fileMod) {
//                $autoPackModel = new AutopackData();
//                $autoPackModel->setAttributes([
//                    'project_id' =>  $project->id,
//                    'file_orig' => $project->fileOriginal->id,
//                    'file_mod' => $project->fileMod->id,
//                    'additions' => implode(',', $project->projectAdditionsIdArray)
//                ]);
//                if (!$autoPackModel->save()) {
//                    return $autoPackModel->errors;
//                }
//
//            }
//        }
//        Yii::$app->response->format = Response::FORMAT_JSON;
//        return AutopackHelper::testAutopack();

//    }

//    public function actionTestPackReal()
//    {
////        Yii::$app->response->format = Response::FORMAT_JSON;
//        $result = AutopackHelper::testAutopackReal();
//        print_r($result);
//        die;
//        return $result;
//// bash /home/<USER>/data/testchiptuning/storage/web/autopack/chip.sh "/home/<USER>/data/testchiptuning/storage/web/autopack/MSG (Renault Traffic 2.0_DCI_90Hp ORIGINAL SW#1037517942).bin" "/home/<USER>/data/diffs/autopack_test/MSG (Renault Traffic 2.0_DCI_90Hp EGR_Off SW#1037517942).bin" "/home/<USER>/data/diffs/autopack_test/MSG (Opel Vivaro 2.0_CDTI_115Hp ORIGINAL SW#1037517942).bin"
//    }
    // -------------------------------------------------------Autopack-----------------------------------------------------------//
//    public function actionTestMail(){
//        Yii::$app->response->format = Response::FORMAT_JSON;
//
//        $request = Yii::$app->request;
//        $mail = $request->get('mail', '<EMAIL>');
//
//        return Yii::$app->commandBus->handle(new SendEmailCommand([
//            'subject' => Yii::t('frontend', 'Activation email'),
//            'view' => '@frontend/mail/activation',
//            'to' => $mail,
//            'params' => [
//                'url' => Yii::getAlias('@frontendUrl').Yii::$app->urlManagerFrontend->createUrl(['/user/sign-in/activation', 'token' => '123123123'])
//            ]
//        ]));
//
//    }


}